.household-manangement-wrapper,
.household-manangement-dialog {
  .input-item {
    border-bottom: 1px solid $UI1B-light-gray-color;

    &::after {
      border: none;
    }
  }

  .error-msg {
    color: $UI1B-red-color;
  }

  .footer-btn {
    color: $UI1B-white-color;
  }

  .room-member-input {
    background-color: transparent;

    .van-field__control {
      color: $UI1B-white-color;
    }
  }

  .van-dialog__header {
    color: $UI1B-font-color;
  }

  .cruise-line-div {
    border: 1px solid $UI1B-light-gray-color;
    background-color: $UI1B-background-color;

    .common-input {
      background-color: transparent;
    }
  }

  .tvt-better-scroll {
    .household-item {
      background: $UI1B-light-background-color;
      color: $UI1B-font-color;

      .household-title {
        color: $UI1B-white-color;

      }

      .household-line-text {
        color: $vms-gray;
      }

      .required-icon {
        color: $vms-red;
      }

      .right-value {
        color: $vms-gray;
      }

      &:not(:last-child) {
        border-bottom: 1px solid $UI1B-light-gray-color;
      }

      &::after {
        border-color: $UI1B-light-gray-color;
      }
    }

    .room-header {
      color: $vms-gray;
    }

    .device-select-icon {
      &.success {
        color: $UI1B-font-color;
      }
    }
  }

  .no-data {
    .add-btn {
      background-color: $UI1B-color-primary;
      color: $UI1B-white-color;
    }
  }

  .no-data-text {
    .footer-btn {
      background-color: $UI1B-color-primary;
      color: $UI1B-white-color;
    }
  }
}