<template>
  <div class="check">
    <div class="container-ul">
      <div class="container-li" v-for="(item, index) in list" :key="'item' + index">
        <div class="li-title">
          <div class="li-title-left">
            <van-checkbox v-model="item.flag">
              <template #icon="props">
                <img
                  class="check-img"
                  :src="
                    props.checked
                      ? require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/check.png')
                      : require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/check_no.png')
                  "
                />
              </template>
            </van-checkbox>
            <div class="li-name text-over-ellipsis">{{ item.devName }}</div>
          </div>
          <div class="check-img" @click="curShow(item)">
            <i :class="item['show'] ? 'arrow-up' : 'arrow-down'"></i>
          </div>
        </div>
        <div :class="item.show ? 'data-ul' : ''" v-show="item.show">
          <div class="v-li" v-for="(v, i) in item.authList" :key="'data' + i">
            <div class="v-li-label">{{ $t(v.label) }}</div>
            <van-checkbox v-model="v.flag">
              <template #icon="props">
                <img
                  class="check-img"
                  :src="
                    v.value === 'config'
                      ? require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/check_disable.png')
                      : props.checked
                      ? require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/check.png')
                      : require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/check_no.png')
                  "
                />
              </template>
            </van-checkbox>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'check',
  components: {},
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      pageContent: navigator.userAgent
    }
  },
  mounted() {},
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    curShow(item) {
      this.$emit('curShow', item)
    }
  }
}
</script>
<style lang="scss" scoped>
.check {
  .container-ul {
    background-color: var(--bg-color-white, #ffffff);
    padding: 2px 0px;
    .container-li {
      border-bottom: 1px solid var(--brand-bg-color-light-disabled, #f2f4f8);
      padding: 0px 15px;
      &:last-child {
        border: 0;
      }
      .check-img {
        width: 20px;
        height: 20px;
        position: relative;
        img {
          width: 100%;
          height: 100%;
        }
        i {
          position: absolute;
          transition: all ease 0.6s;
          top: 2px;
          width: 14px;
          height: 14px;
          display: inline-block;
          background-image: url('../assets/img/common/arrow_up.png');
          background-repeat: no-repeat;
          background-size: 14px;
          background-position-y: center;
          background-position-x: center;
        }
      }
      .arrow-up {
        transform: rotate(0deg);
      }
      .arrow-down {
        transform: rotate(180deg);
      }
      .li-title {
        display: flex;
        justify-content: space-between;
        height: 50px;
        align-items: center;
        .li-title-left {
          display: flex;
        }
        .li-name {
          margin-left: 10px;
          max-width: 280px;
          font-weight: 500;
        }
      }
    }
    .data-ul {
      border-top: 1px solid var(--brand-bg-color-light-disabled, #f2f4f8);
      padding: 4px 0;
      font-size: var(--font-size-body2-size, 14px);
      color: var(--text-color-placeholder, #8f8e93);
    }
    .v-li {
      padding: 6px 0 6px 30px;
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
