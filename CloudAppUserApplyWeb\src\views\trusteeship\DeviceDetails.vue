<template>
  <div class="trusteeship-device-details">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="device-details">
      <div class="title common text-over-ellipsis">{{ deviceInfo.devName }}</div>
      <div class="time common">
        {{ $t('trusteeshipTime') }}{{ deviceInfo.createTime ? dateFormat(parseInt(deviceInfo.createTime)) : '' }}
      </div>
      <div class="status common" v-if="deviceInfo.status == 0">{{ statusLabel(deviceInfo.status, statusOptions) }}</div>
    </div>
    <div class="configuration">
      <div class="configuration-label">{{ $t('trusteeshipPermissions') }}</div>
      <div @click="editPermission" v-if="!(appType === 'TOB' && vmsUserType == 2)"><van-icon name="edit" /></div>
    </div>
    <div class="container-ul">
      <div class="device-configuration" v-for="(item, index) in deviceInfo.authList" :key="'config' + index">
        <div class="configuration-item">
          <div class="item-label">{{ $t(item.label) }}</div>
          <img
            class="item-img"
            alt="check"
            :class="uiStyleFlag === 'UI1B' ? 'vms-item-img' : ''"
            :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/checked.png')"
            v-if="item.flag"
          />
        </div>
      </div>
    </div>
    <!-- 新加的 有效期编辑 -->
    <div class="v-li" v-if="!(appType === 'TOB' && vmsUserType == 2)">
      <div class="v-li-content">
        <div class="v-li-label">{{ $t('permissionValidity') }}</div>
      </div>
      <div class="v-li-res" @click.stop="editValidity">
        <div class="v-li-value">{{ timeMethod(deviceInfo) }}</div>
        <div class="v-li-icon"><i class="arrow-right"></i></div>
      </div>
    </div>
    <div class="footer" v-if="!(appType === 'TOB' && vmsUserType == 2)">
      <div class="footer-btn" @click="cancelTrusteeship">
        {{ $t('cancelTrusteeship') }}
      </div>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { appSetTitle, appClose } from '@/utils/appbridge'
import { dateFormat, timeMethod } from '@/utils/common'
import { TRUSTEESHIP_STATUS, AUTH_LIST, TRUSTEESHIP_VALIDITY_LIST } from '@/utils/options.js'
import { statusLabel } from '@/utils/trusteeship.js'
import { deviceTrusteeshipsGet, deviceTrusteeshipsDelete } from '@/api/trusteeship.js'
import { mapState, mapMutations } from 'vuex'
export default {
  name: 'deviceDetails',
  components: {
    NavBar
  },
  data() {
    return {
      pageContent: navigator.userAgent,
      statusOptions: TRUSTEESHIP_STATUS(),
      authListOptions: AUTH_LIST(),
      dateFormat: dateFormat,
      timeMethod: timeMethod,
      statusLabel: statusLabel,
      deviceInfo: {},
      vmsUserType: 0, //1：上级用户 2：下级用户
      validityList: TRUSTEESHIP_VALIDITY_LIST()
    }
  },
  mounted() {
    appSetTitle(this.$t('details'))
    this.vmsUserType = this.$route.params.userType || 0
    this.getDetails({ id: this.$route.params.id })
  },
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('trusteeship', ['SET_AVAILABLE_LIST']),
    back() {
      this.$router.go(-1)
    },
    // 编辑权限 把信息存过去
    editPermission() {
      this.SET_AVAILABLE_LIST([this.deviceInfo])
      this.$router.push({ name: 'permission', params: { index: 0, from: 'edit', id: this.$route.params.id } })
    },
    // 编辑有效期 把信息存过去
    editValidity() {
      this.SET_AVAILABLE_LIST([this.deviceInfo])
      this.$router.push({ name: 'validity', params: { index: 0, from: 'edit', id: this.$route.params.id } })
    },
    resValidity(item) {
      let str = ''
      let i = this.validityList.findIndex(o => o.value == item.effectiveTime)
      if (i !== -1) {
        str = this.validityList[i].label
      }
      return str
    },
    dealData(obj) {
      let authList = []
      this.authListOptions.forEach(item => {
        let auth = obj.authList || []
        let index = auth.findIndex(v => v == item.value)
        authList.push({ label: item.value, value: item.value, flag: index != -1 })
      })
      let newObj = { ...obj, authList }
      return newObj
    },
    // 查询托管详情
    getDetails(param) {
      this.$loading.show()
      deviceTrusteeshipsGet(param)
        .then(({ data }) => {
          this.$loading.hide()
          console.log(data, '查询托管详情')
          this.deviceInfo = this.dealData(data)
        })
        .catch(error => {
          this.$loading.hide()
          if (!error.basic) return
          if (error.basic.code === 32019 || error.basic.code === 32021) {
            this.back()
          } else if (error.basic.code === 32018) {
            setTimeout(() => {
              appClose()
            }, 1000)
          }
        })
    },
    cancelTrusteeship() {
      let tips = {
        message: this.$t('cancelTrusteeshipTip'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }
      if (this.style == 'UI1B') {
        tips['title'] = this.$t('tips')
      }
      this.$dialog
        .confirm(tips)
        .then(() => {
          if (!this.deviceInfo.sn) {
            //没有数据时 提示操作失败 返回到列表页
            this.$toast(this.$t('operationFail'))
            this.back()
            return
          }
          this.$loading.show()
          let param = { sn: this.deviceInfo.sn }
          deviceTrusteeshipsDelete(param)
            .then(res => {
              this.$loading.hide()
              console.log(res, '取消托管的返回')
              if (res.basic.code == 200) {
                this.$toast(this.$t('operationSuccess'))
                this.back()
              }
            })
            .catch(error => {
              this.$loading.hide()
              if (!error.basic) return
              if (error.basic.code === 32019 || error.basic.code === 32021) {
                this.back()
              } else if (error.basic.code === 32018) {
                setTimeout(() => {
                  appClose()
                }, 1000)
              }
            })
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.trusteeship-device-details {
  height: calc(100% - 70px);
  overflow: auto;
  .device-details {
    margin-top: 15px;
    padding: 8px 15px;
    background-color: var(--bg-color-white, #ffffff);
    font-weight: 500;
    font-size: var(--font-size-text-size, 12px);
    color: var(--text-color-placeholder, #8f8e93);
    .title {
      height: 32px;
      line-height: 32px;
      font-size: var(--font-size-body1-size, 16px);
      color: var(--icon-color-primary, #393939);
    }
    .common {
      padding: 4px 0px;
    }
  }
  .configuration {
    height: 32px;
    line-height: 32px;
    padding-left: 15px;
    padding-right: 15px;
    color: var(--text-color-placeholder, #8f8e93);
    display: flex;
    justify-content: space-between;
  }
  .device-configuration {
    background-color: var(--bg-color-white, #ffffff);
    height: 50px;
    line-height: 50px;
    font-size: var(--font-size-body1-size, 16px);
    font-weight: 500;
    border-bottom: 1px solid var(--brand-bg-color-light-disabled, #f2f4f8);
    .configuration-item {
      padding: 0 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .item-img {
        width: 15px;
        height: 15px;
      }
      .vms-item-img {
        width: 24px;
        height: 24px;
      }
    }
  }
  .v-li {
    margin-top: 15px;
    background-color: var(--bg-color-white, #ffffff);
    padding: 6px 0 6px 15px;
    display: flex;
    line-height: 32px;
    font-size: var(--font-size-body1-size, 16px);
    font-weight: 500;
    justify-content: space-between;
    align-items: center;
    .v-li-content {
      display: flex;
      justify-content: space-between;
    }
    .v-li-res {
      display: flex;
      align-items: center;
      .v-li-value {
        text-align: right;
        padding-right: 10px;
      }
    }
    .v-li-icon {
      width: 20px;
      height: 20px;
      position: relative;
      i {
        position: absolute;
        transition: all ease 0.6s;
        top: 2px;
        width: 14px;
        height: 14px;
        display: inline-block;
        background-image: url('@/assets/img/common/arrow_up.png');
        background-repeat: no-repeat;
        background-size: 14px;
        background-position-y: center;
        background-position-x: center;
      }
    }
    .arrow-right {
      transform: rotate(90deg);
    }
  }
}
</style>
