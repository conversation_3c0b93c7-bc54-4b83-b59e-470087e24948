<template>
  <div class="restore-password-step">
    <div class="restore-title">Verify your Email</div>
    <div class="restore-subtitle">Please check your Email and input the authentication code</div>

    <div class="restore-form">
      <input type="text" class="restore-input" v-model="authCode" placeholder="Authentication Code" maxlength="6" />
    </div>

    <div class="restore-actions">
      <van-button class="action-btn" @click="handleNext">Next</van-button>
      <van-button class="action-btn cancel-btn" @click="handleCancel">Cancel</van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RestorePasswordStep2',
  props: {
    email: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      authCode: ''
    }
  },
  methods: {
    handleNext() {
      if (!this.authCode) {
        this.$toast('Please enter the authentication code')
        return
      }

      // 验证码应该是6位数字
      if (!/^\d{6}$/.test(this.authCode)) {
        this.$toast('Please enter a valid 6-digit authentication code')
        return
      }

      this.$emit('next', {
        email: this.email,
        authCode: this.authCode
      })
    },
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.restore-password-step {
  padding: 20px;
  background-color: #333;

  .restore-title {
    font-size: 20px;
    color: #fff;
    margin-bottom: 16px;
  }

  .restore-subtitle {
    font-size: 14px;
    color: #fff;
    margin-bottom: 20px;
  }

  .restore-form {
    margin-bottom: 30px;

    .restore-input {
      width: 100%;
      height: 40px;
      background-color: #fff;
      border: none;
      padding: 0 12px;
      font-size: 16px;
      box-sizing: border-box;
    }
  }

  .restore-actions {
    display: flex;
    justify-content: space-between;

    .action-btn {
      width: 48%;
      height: 40px;
      background-color: #999;
      border: none;
      border-radius: 4px;
      color: #fff;
      font-size: 16px;
    }

    .cancel-btn {
      background-color: #777;
    }
  }
}
</style>
