<template>
  <van-popup v-model="visible" :close-on-click-overlay="false" class="restore-password-popup">
    <restore-password-step1 v-if="currentStep === 1" @next="goToStep2" @cancel="handleCancel" />

    <restore-password-step2 v-if="currentStep === 2" :email="formData.email" @next="goToStep3" @cancel="handleCancel" />

    <restore-password-step3
      v-if="currentStep === 3"
      :email="formData.email"
      :authCode="formData.authCode"
      @submit="handleSubmit"
      @cancel="handleCancel"
    />
  </van-popup>
</template>

<script>
import RestorePasswordStep1 from './RestorePasswordStep1.vue'
import RestorePasswordStep2 from './RestorePasswordStep2.vue'
import RestorePasswordStep3 from './RestorePasswordStep3.vue'

export default {
  name: 'RestorePassword',
  components: {
    RestorePasswordStep1,
    RestorePasswordStep2,
    RestorePasswordStep3
  },
  props: {
    value: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  data() {
    return {
      currentStep: 1,
      formData: {
        email: '',
        authCode: '',
        newPassword: ''
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    goToStep2(data) {
      this.formData.email = data.email

      // 这里可以添加发送验证码的逻辑
      this.$toast.loading({
        message: 'Sending verification code...',
        forbidClick: true,
        duration: 0
      })

      // 模拟发送验证码
      setTimeout(() => {
        this.$toast.clear()
        this.$toast.success('Verification code sent')
        this.currentStep = 2
      }, 1500)
    },
    goToStep3(data) {
      this.formData.authCode = data.authCode

      // 这里可以添加验证码验证的逻辑
      this.$toast.loading({
        message: 'Verifying code...',
        forbidClick: true,
        duration: 0
      })

      // 模拟验证码验证
      setTimeout(() => {
        this.$toast.clear()
        this.$toast.success('Code verified')
        this.currentStep = 3
      }, 1500)
    },
    handleSubmit(data) {
      this.formData.newPassword = data.newPassword

      // 这里可以添加重置密码的逻辑
      this.$toast.loading({
        message: 'Resetting password...',
        forbidClick: true,
        duration: 0
      })

      // 模拟重置密码
      setTimeout(() => {
        this.$toast.clear()
        this.$toast.success('Password reset successfully')
        this.handleCancel(true) // 成功后关闭弹窗
      }, 1500)
    },
    handleCancel(isSuccess = false) {
      this.visible = false
      this.currentStep = 1
      this.formData = {
        email: '',
        authCode: '',
        newPassword: ''
      }

      if (isSuccess) {
        this.$emit('success')
      } else {
        this.$emit('cancel')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.restore-password-popup {
  width: 80%;
  max-width: 350px;
  border-radius: 8px;
  overflow: hidden;
}
</style>
