// 插件 涉及的一些常用js 处理方法
import Vue from 'vue'
import i18n from '@/lang'
import axios from 'axios'
import { TRUSTEESHIP_VALIDITY_LIST } from '@/utils/options.js'

// 用的基础控件库处理xml的方法
export function transformXml(xmls) {
  //创建xml对象
  const { $TvtXml2Json } = Vue.prototype
  return $TvtXml2Json.dom2js($TvtXml2Json.xml2dom(xmls))
}
// 获取xml解析成js后，里面的cdata,string等数据
export function formatXmlData(obj) {
  if (typeof obj !== 'object') {
    return obj
  }

  if (obj['#cdata-section'] !== undefined) {
    return obj['#cdata-section']
  }

  if (obj['_text'] !== undefined) {
    return obj['_text']
  }

  if (obj['__text'] !== undefined) {
    return obj['__text']
  }

  if (Array.isArray(obj.enum)) {
    return obj.enum
  }

  return obj
}

// 根据chlIndex 得到chlId  {0000000F-0000-0000-0000-000000000000}
export function getChlGuid16(chlIndex) {
  const id = parseInt(chlIndex).toString(16)
  let str = ''
  if (id.length < 8) {
    for (let i = 0; i < 8 - id.length; i++) {
      str += '0'
    }
  }
  str += id
  const arr = [str, '0000', '0000', '0000', '000000000000']
  const guid = '{' + arr.join('-') + '}'
  return guid
}

export function transformObjToXml(obj) {
  const { $TvtXml2Json } = Vue.prototype
  return $TvtXml2Json.js2xml(obj)
}

//通过navigator.userAgent 获取版本号和UI
export function getVersionAndStyle() {
  let newArr = []
  newArr = navigator.userAgent.split(' ')
  let newStr = newArr[newArr.length - 1]
  let msgArr = newStr.split('/')
  let language = msgArr[4]
  let style = msgArr[3]
  let version = msgArr[2]
  let appType = msgArr[1]
  let type = msgArr[0]
  return { version, style, language, type, appType }
}

export function keyFindValue(key, str) {
  let arr = str.split(key + '/')
  if (arr && arr[1]) {
    let newArr = arr[1].split(' ')
    return newArr[0]
  }
}

// 通过navigator.userAgent 获取相关参数  platf/an aVer/1.0.1 aName/plus aUI/UI1A aType/TOB aLang/en
export function getParamsFromUserAgent() {
  let appPlatform = keyFindValue('platf', navigator.userAgent) // an IOS
  let version = keyFindValue('aVer', navigator.userAgent)
  let appName = keyFindValue('aName', navigator.userAgent)
  let style = keyFindValue('aUI', navigator.userAgent)
  let appType = keyFindValue('aType', navigator.userAgent)
  let language = keyFindValue('aLang', navigator.userAgent)
  let bridgeType = keyFindValue('bridgeType', navigator.userAgent)
  let top = keyFindValue('top', navigator.userAgent) || 0
  let bottom = keyFindValue('bottom', navigator.userAgent) || 0
  let appId = keyFindValue('appId', navigator.userAgent) || ''
  return { version, style, language, appPlatform, appType, appName, bridgeType, top, bottom, appId }
}

// 判断现有的H5里面是否有app传过来的匹配的UI 没有的话用默认中性UI
export function isExistTypeUi(aType, aUI, list) {
  let type = aType
  let UI = aUI
  let flag = false
  list.forEach(item => {
    if (item.aType === aType && item.aUI === aUI) {
      flag = true
    }
  })
  if (!flag) {
    if (aType == 'TOB') {
      UI = 'UI1B'
    } else if (aType == 'TOP') {
      UI = 'UI1C'
    } else {
      UI = 'UI1A'
    }
  }
  return { type, UI }
}

// 解析URL参数 http://10.50.20.27:9020/?hide_title=1&devId=5D52F51B15984AE69EBA671D6193903C
// &devName=DeviceName1&bindState=0#/cloud/upgrade
export function getUrlQuery(url) {
  let frontStr = url.substr(0, url.indexOf('#'))
  let str = frontStr.substr(frontStr.indexOf('?') + 1)
  let json = new Object()
  const arr = str.split('&')
  for (const element of arr) {
    let item = element.split('=')
    json[item[0]] = item[1]
  }
  return json
}

// 解析superlive max的URL参数 http://10.50.20.27:9020/#/cloud/upgrade?hide_title=1&devId=5D52F51B15984AE69EBA671D6193903C
// &devName=DeviceName1&bindState=0
export function getMaxUrlQuery(url) {
  let str = url.substr(url.indexOf('?') + 1)
  let json = new Object()
  const arr = str.split('&')
  for (const element of arr) {
    let item = element.split('=')
    json[item[0]] = item[1]
  }
  return json
}

// 时间戳格式化
export const dateFormat = (timestemp, format = 'YYYY-MM-DD HH:mm:ss') => {
  return window.moment(+new Date(timestemp)).format(format) || ''
}

export const strToDate = str => {
  const timestamp = window.moment(str).valueOf()
  return new Date(timestamp)
}

// eslint-disable-next-line prettier/prettier
export const doNothing = () => { }

// 判断PC端和移动端
export function isPhone() {
  let flag = true
  const system = {
    win: false,
    mac: false,
    xll: false
  }
  const p = navigator.platform
  system.win = p.startsWith('Win')
  system.mac = p.startsWith('Mac') && navigator.maxTouchPoints == 0 // ipad在ios13之后platform改变为MacIntel maxTouchPoints大于0表示是触摸屏
  system.x11 = p == 'X11' || p.startsWith('Linux')
  if (system.win || system.mac || system.xll) {
    flag = false
  }
  return flag
}

// 对象深拷贝
export function deepCopy(obj) {
  let target = null
  if (typeof obj === 'object') {
    if (Array.isArray(obj)) {
      //数组
      target = []
      obj.forEach(item => {
        target.push(deepCopy(item))
      })
    } else if (obj) {
      target = {}
      let objKeys = Object.keys(obj)
      objKeys.forEach(key => {
        target[key] = deepCopy(obj[key])
      })
    } else {
      target = obj
    }
  } else {
    target = obj
  }
  return target
}

// 将相差的时间戳转成多少天多少小时多少分钟多少秒 传参是13位毫秒级
export const convertTimestamp = (startTime, endTime) => {
  const seconds = Math.floor(endTime - startTime) / 1000
  const days = Math.floor(seconds / (3600 * 24))
  const hours = Math.floor((seconds % (3600 * 24)) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return { days: days, hours: hours, minutes: minutes, seconds: remainingSeconds }
}

// 处理显示时间 显示到分钟级 但计算是秒级
export const showTimeStr = (startTime, endTime) => {
  let str = i18n.t('residue') + ' '
  let obj = convertTimestamp(startTime, endTime)
  let { days, hours, minutes } = obj
  if (days) {
    str += i18n.t('manyDays', [days])
  }
  if (hours) {
    str += i18n.t('manyHours', [hours])
  }
  if (minutes) {
    str += i18n.t('manyMinutes', [minutes])
  }
  return str
}

export function timeMethod(item) {
  let str = ''
  let validityList = TRUSTEESHIP_VALIDITY_LIST()
  const nowTimeStamp = Date.parse(new Date())
  const { status, effectiveTime, expireTime } = item
  if (!effectiveTime || !expireTime) return
  if (status == 1 && parseInt(effectiveTime)) {
    // 剩余时长小于1min
    if (parseInt(expireTime) - parseInt(nowTimeStamp) < 1000 * 60) {
      str = i18n.t('expired')
    } else {
      str = showTimeStr(nowTimeStamp, parseInt(expireTime))
    }
  } else {
    const index = validityList.findIndex(v => v.value === parseInt(effectiveTime))
    str = validityList[index].label
  }
  return str
}

// 处理显示时间 显示到秒 但计算是秒级
export const showTimeStr2 = (startTime, endTime) => {
  let str = ''
  let obj = convertTimestamp(startTime, endTime)
  let { days, hours, minutes, seconds } = obj
  if (days) {
    str += i18n.t('manyDays', [days])
  }
  if (hours) {
    str += i18n.t('manyHours', [hours])
  }
  if (minutes) {
    str += i18n.t('manyMinutes', [minutes])
  }
  if (seconds) {
    str += i18n.t('manySeconds', [seconds])
  }
  return str
}
export function expireTimeMethod(expireTime) {
  let str = ''
  const nowTimeStamp = Date.parse(new Date())
  if (Number(expireTime) === 0) {
    // expireTime为0表示永久
    str = i18n.t('forever')
  } else if (parseInt(expireTime) - parseInt(nowTimeStamp) < 1000) {
    // 剩余时长小于1s
  } else {
    str = showTimeStr2(nowTimeStamp, parseInt(expireTime))
  }
  return str
}

export function ifDevelopment(cb) {
  if (process.env.NODE_ENV === 'development') {
    cb()
  }
}

// 早期存储的用户手机号是86+123123123
// 只能兼容，然后转换成正确的格式 +86 123123123
export function formatPhoneNumber(str) {
  if (!str) {
    return ''
  }

  if (!/^[\d+]+$/.test(str)) {
    return str
  }

  const index = str.indexOf('+')
  if (index === -1 || index === 0) {
    return str
  }

  const [code, number] = str.split('+')

  return `+${code} ${number}`
}

// 防抖函数
export function debounce(fn, delay = 1000) {
  let timeout = null
  return function (...args) {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 图片转base64
export function converImageToBase64(imgUrl, callback) {
  const image = new Image()
  image.crossOrigin = 'anonymous'
  image.onload = () => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    canvas.height = image.naturalHeight
    canvas.width = image.naturalWidth
    ctx.drawImage(image, 0, 0)
    const dataUrl = canvas.toDataURL()
    if (callback) {
      callback(dataUrl)
    }
  }
  image.src = imgUrl
}

// 目标项目中加载全部语言包
export async function loadLocales(src, callback) {
  try {
    // console.log('loadLocales src', src)
    // 一、使用fetch方法获取数据
    // const response = await fetch(src)
    // const data = await response.json()
    // if (callback) callback(data)
    // return data

    // 二、使用axios方法获取数据
    const res = await axios.get(src)
    if (callback) callback(res.data)
    return res.data
  } catch (error) {
    console.error('Failed to load locales:', error)
    if (callback) callback(null)
  }
}

// 加载远程JS文件
export function loadRemoteScript(src, callback) {
  const script = document.createElement('script')
  script.src = src
  script.onload = () => {
    console.log('Script loaded and ready')
    // 在这里可以调用远程脚本中定义的函数或访问其变量
    if (callback) callback()
  }
  script.onerror = () => {
    console.error('Error loading script')
  }
  document.head.appendChild(script)
}

// 根据appType和UI来确定远程CSS文件的路径
export const appPathObj = {
  TOB: 'vmsstatic',
  TOC: 'userstatic',
  TOP: 'partnerstatic'
}

// 获取远程文件的路径
export const getRemoteBasePath = (appType = 'TOC') => {
  return `/${appPathObj[appType] || 'userstatic'}`
}

// 远程加载APP定制主题变量文件--MAX中用到
export const loadRemoteVariableCSS = async (appType = 'TOC', UI = 'UI2B') => {
  console.log('loadRemoteVariableCSS')
  if (appType !== 'TOC') {
    // 暂时不加载定制主题变量文件
    return
  }

  // 加载远程CSS文件的基础URL
  const basePath = getRemoteBasePath(appType)
  const cssUrl = `${basePath}/theme-config/base/variables.css` // 基础主题变量文件
  const maxThemeName = window.maxThemeUiList?.[`${appType}-${UI}`] // max定制主题名称

  // 创建加载CSS的辅助函数
  const loadCSS = url => {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = url
      link.onload = () => resolve(url)
      link.onerror = () => {
        document.head.removeChild(link)
        console.warn(`Failed to load remote CSS: ${url}`)
        reject(new Error(`Failed to load CSS: ${url}`))
      }
      document.head.appendChild(link)
    })
  }

  try {
    const loadPromises = []

    // 始终加载基础主题变量文件
    loadPromises.push(loadCSS(cssUrl))
    console.log('Loading base theme CSS:', cssUrl)

    // 如果存在max定制主题名称，同时加载定制主题文件
    if (maxThemeName) {
      const maxThemeUrl = `${basePath}/theme-config/${maxThemeName}/variables.css`
      loadPromises.push(loadCSS(maxThemeUrl))
      console.log('Loading max theme CSS:', maxThemeUrl)
    }

    // 等待所有CSS文件加载完成
    await Promise.all(loadPromises)
    console.log('All theme CSS files loaded successfully')
  } catch (error) {
    console.error('Error loading remote CSS:', error)
    // 发生错误时可以选择加载默认样式
    throw error
  }
}

// 远程加载定制主题的css文件
export const loadRemoteCSS = async (appType = 'TOC', UI = 'UI1A') => {
  // 假设远程CSS文件的基础URL
  const basePath = getRemoteBasePath(appType)
  const cssUrl = `${basePath}/${UI.toLocaleLowerCase()}/theme/theme.css`
  try {
    // 如果指定了定制主题，则加载定制主题
    if (!(appType && UI && window.themeUiList.find(item => item.aType === appType && item.aUI === UI))) {
      return
    }

    // 创建link标签加载远程CSS
    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = cssUrl
    document.head.appendChild(link)

    // 返回一个Promise，在CSS加载完成后resolve
    return new Promise((resolve, reject) => {
      link.onload = () => resolve()
      link.onerror = () => {
        document.head.removeChild(link)
        console.warn(`Failed to load remote CSS: ${cssUrl}, falling back to default style`)
        reject()
      }
    })
  } catch (error) {
    console.error('Error loading remote CSS:', error)
    // 发生错误时加载默认样式
    // return import('@/assets/css/default/index.scss')
    return
  }
}

/**
 * 统一处理语言代码
 * @param {string} language 原始语言代码
 * @returns {string} 标准化后的语言代码
 */
export function normalizeLanguageCode(language) {
  const languageMap = {
    zh: 'zh-CN',
    en: 'en-US',
    de: 'de-DE',
    es: 'es-ES',
    fr: 'fr-FR',
    it: 'it-IT',
    pt: 'pt-PT',
    vi: 'vi-VN',
    ua: 'ua-UA',
    nl: 'nl-NL',
    lt: 'lt-LT',
    he: 'he-HE',
    iw: 'iw-IW',
    pl: 'pl-PL',
    nb: 'nb-NB',
    ar: 'ar-AR',
    da: 'da-DA',
    fi: 'fi-FI',
    ru: 'ru-RU',
    uk: 'uk-UK',
    tr: 'tr-TR',
    sk: 'sk-SK',
    ja: 'ja-JP'
  }

  return languageMap[language] || language
}

// 格式化文件大小
export function formatFileSize(bytes) {
  if (bytes === 0) {
    return '0 B'
  }

  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)))

  let value = bytes / Math.pow(1024, i)
  if (i >= 3) {
    // GB (index 3) or larger
    value = value.toFixed(1)
  } else {
    value = value.toFixed(0)
  }

  return value + ' ' + sizes[i]
}
