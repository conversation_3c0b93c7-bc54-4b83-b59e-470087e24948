<template>
  <div class="restore-password-step">
    <div class="restore-title">Set New Password</div>
    <div class="restore-subtitle">Please set a new password for your account</div>

    <div class="restore-form">
      <input
        :type="showPassword ? 'text' : 'password'"
        class="restore-input"
        v-model="password"
        placeholder="New Password"
      />

      <input
        :type="showConfirmPassword ? 'text' : 'password'"
        class="restore-input"
        v-model="confirmPassword"
        placeholder="Confirm the new Password"
        style="margin-top: 16px"
      />
    </div>

    <div class="restore-actions">
      <van-button class="action-btn" @click="handleSubmit">Submit</van-button>
      <van-button class="action-btn cancel-btn" @click="handleCancel">Cancel</van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RestorePasswordStep3',
  props: {
    email: {
      type: String,
      required: true
    },
    authCode: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      password: '',
      confirmPassword: '',
      showPassword: false,
      showConfirmPassword: false
    }
  },
  methods: {
    handleSubmit() {
      if (!this.password) {
        this.$toast('Please enter a new password')
        return
      }

      if (this.password.length < 6) {
        this.$toast('Password must be at least 6 characters')
        return
      }

      if (this.password !== this.confirmPassword) {
        this.$toast('Passwords do not match')
        return
      }

      // 提交重置密码的请求
      this.$emit('submit', {
        email: this.email,
        authCode: this.authCode,
        newPassword: this.password
      })
    },
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.restore-password-step {
  padding: 20px;
  background-color: #333;

  .restore-title {
    font-size: 20px;
    color: #fff;
    margin-bottom: 16px;
  }

  .restore-subtitle {
    font-size: 14px;
    color: #fff;
    margin-bottom: 20px;
  }

  .restore-form {
    margin-bottom: 30px;

    .restore-input {
      width: 100%;
      height: 40px;
      background-color: #fff;
      border: none;
      padding: 0 12px;
      font-size: 16px;
      box-sizing: border-box;
    }
  }

  .restore-actions {
    display: flex;
    justify-content: space-between;

    .action-btn {
      width: 48%;
      height: 40px;
      background-color: #999;
      border: none;
      border-radius: 4px;
      color: #fff;
      font-size: 16px;
    }

    .cancel-btn {
      background-color: #777;
    }
  }
}
</style>
