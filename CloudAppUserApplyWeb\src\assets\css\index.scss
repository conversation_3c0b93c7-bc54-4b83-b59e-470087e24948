@import './variables.scss';
@import './mixin.scss';
@import './common/common.scss';
@import './iconfont/iconfont.css';

html body {
  -webkit-text-size-adjust: 100%; // 防止IOS在横屏模式下放大文字
}

html,
body,
#app {
  width: 100%;
  height: 100%;
  font-size: 14px;
  font-weight: 500;
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'Hiragino Sans GB', ' Microsoft YaHei', Arial, sans-serif;
  overflow-x: hidden;
}
* {
  padding: 0;
  margin: 0;
  border: 0;
}
ul,
li {
  list-style: none;
}

.text-over-ellipsis {
  overflow: hidden;        // 超出的文本隐藏
  text-overflow: ellipsis; // 溢出用省略号显示
  white-space: nowrap;     // 溢出不换行
}

.text-over-ellipsis2 {
  overflow: hidden;        // 超出的文本隐藏
  text-overflow: ellipsis; // 溢出用省略号显示
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}


// 设置所有 input 元素的 placeholder 的文字颜色和字号
input::-webkit-input-placeholder { 
  /* WebKit browsers，webkit内核浏览器 */
  color: $placeholder-color; 
  font-size: 14px;
  } 
  input:-moz-placeholder { 
  /* Mozilla Firefox 4 to 18 */ 
  color: $placeholder-color; 
  font-size: 14px;
  } 
  input::-moz-placeholder { 
  /* Mozilla Firefox 19+ */ 
  color: $placeholder-color; 
  font-size: 14px;
  } 
  input:-ms-input-placeholder { 
  /* Internet Explorer 10+ */ 
  color: $placeholder-color; 
  font-size: 14px;
  }
  
// word-break: break-all; 设置文字的强制自动换行，但只对英文起作用，以字母作为换行依据。
// word-wrap: break-word; 设置文字的强制自动换行，但只对英文起作用，以单词作为换行依据
.van-toast {
  word-break: break-word;
}

.van-toast--text {
  width: max-content;
}

.van-swipe-cell__right {
  right: -1px;
}
