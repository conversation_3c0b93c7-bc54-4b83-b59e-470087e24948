export default {
  upgrade: 'Cloud Upgrade',
  cancel: 'Cancel',
  confirm: 'Confirm',
  deviceUpdate: 'Device',
  cameraUpdate: 'Camera',
  allUpdate: 'All Upgrade',
  updateNow: 'Upgrade',
  currentVersion: 'Current Version',
  latestVersion: 'Latest Version',
  updateContent: 'Update Content',
  hasLatestVersion: 'Already the latest version',
  online: 'Online',
  offline: 'Offline',
  waitDownload: 'Waiting for download',
  inprogress: 'Downloading',
  downloadFail: 'Download failed',
  downloadFinished: 'Download complete',
  waitingForUpgrade: 'Waiting for upgrade',
  inupgrade: 'Upgrading',
  upgradeFail: 'Update failed',
  upgradeSuccess: 'Upgrade succeeded',
  deviceUpgradeInfo:
    'During the upgrade, the device will be disconnected and automatically restarted. Are you sure to upgrade?',
  upgradeTip:
    'After the download is complete, the device will automatically upgrade. During the upgrade process, the device will automatically restart. Please do not manually restart the device or disconnect the power supply until the automatic restart is complete.',
  cameraUpgradeInfo:
    'During the upgrade, the camera will be disconnected and automatically restarted. Are you sure to upgrade?',
  pwdUserNameError: 'Username or password error',
  permissionAuth: 'Super administrator authority authentication',
  pleaseEnterUser: 'Please enter user name',
  pleaseEnterPwd: 'Please enter password',
  noCameraUpgrade: 'No upgradeable camera detected',
  handleCheck: 'Check for Upgrades',
  checkSuccess: 'Check successfully',
  checkFail: 'Check failed',
  viewUpdateContent: 'View update content',
  deviceDisconnected: 'Failed to connect device',
  updateNote: 'Update note',
  noData: 'No Data',
  tips: 'Tips',
  paySuccess: 'Payment succeeded',
  payFail: 'Payment failed',
  done: 'Complete',
  rePurchase: 'Repurchase',
  cloudStorage: 'Cloud Storage',
  INSTRUMENT_DECLINED: 'Transaction exceeds card limit',
  PAYER_ACCOUNT_LOCKED_OR_CLOSED: 'Payer account cannot be used for this transaction',
  PAYER_ACCOUNT_RESTRICTED: 'Payer account is restricted',
  TRANSACTION_LIMIT_EXCEEDED: 'The total payment amount exceeds the transaction limit',
  TRANSACTION_RECEIVING_LIMIT_EXCEEDED: 'The transaction exceeds the recipient receiving limit',
  fail: 'Transaction failed',
  // 设备托管
  myInstaller: 'My Installer',
  trusteeshipDevice: 'Managed device',
  addTrusteeship: 'Authorize More',
  waitReceived: 'To be received',
  received: 'Received',
  refuse: 'Refuse',
  delete: 'Delete',
  operationSuccess: 'Operation succeeded',
  operationFail: 'Operation failed',
  cancelTrusteeship: 'Cancel Authorization',
  chooseDevice: 'Select device',
  noAvaiableDevice:
    'Only devices that support being authorized for management and have been added through binding can be authorized.',
  leastChoose: 'Select at least one device',
  finish: 'Complete',
  details: 'Details',
  deviceDetails: 'Details',
  live: 'Live View',
  rec: 'Playback',
  config: 'Configuration',
  confirmTrusteeshipTip:
    'The authorization request has been sent to the installer. Please wait for them to process it.',
  cancelTrusteeshipTip:
    'After canceling authorization, the installer is unable to provide you with remote maintenance services. Confirm cancellation?',
  unBindTrusteeship: 'After unbinding, all authorized management will be canceled. Are you sure you want to unbind?',
  trusteeshipPermissions: 'Permissions',
  trusteeshipTime: 'Duration',
  unBind: 'Unbind',
  serviceException: 'Service exception',
  netTimeOut: 'Network connection timeout',
  pullingText: 'Loading immediately after releasing',
  loosingText: 'Refresh immediately after releasing',
  loosing: 'Refreshing...',
  loadingText: 'Loading...',
  refreshComplete: 'Refresh successfully',
  noMore: 'No more...',
  noMoreDevice: 'No available devices',
  invalid: 'Expired',
  // 1.14.0
  password: 'Password',
  ipcUpgrade: 'Cloud Upgrade',
  pwdError: 'Password error, you can try {0} times',
  pwdErrorLock: 'Too many errors have been locked, please try again later!',
  noPermissions: 'No permission',
  permission: 'Permission',
  validity: 'Validity',
  permissionValidity: 'Authorize',
  isSaveModify: 'Do you want to save the changes?',
  manyMinutes: '{0}min',
  manyHours: '{0}h',
  manyDays: '{0}d',
  manyMinutesEn: '{0} minute(s)',
  manyHoursEn: '{0} hour(s)',
  manyDaysEn: '{0} day(s)',
  oneWeek: '1 week',
  forever: 'Permanent',
  expired: 'Expired',
  residue: 'Remaining time',
  // 转移请求
  transferRequest: 'Transfer request',
  acceptTransfer: 'Accept',
  refuseTransferConfirm: 'Are you sure to decline the transfer?',
  bindInstallerText: 'The authorization service is available after you bind the Installer({account}). Bind now?',
  bindSuccess: 'Binding successful',
  acceptSuccess: 'Accept successfully',
  from: 'From',
  // 分享管理--除superlive plus外
  shareManage: 'Share Management',
  shareDetail: 'Sharing Detail',
  acceptShare: 'Accept sharing',
  permissionText: 'The permissions you have obtained',
  livePreview: 'Live preview',
  playback: 'Playback',
  alarm: 'Alarm',
  intercom: 'Intercom',
  gimbal: 'PTZ',
  refuseShareConfirm: 'Confirm refusal of share?',
  acceptAll: 'Accept All',
  exitShare: 'Exit sharing',
  changeCapability: 'Set capability',
  deviceOperation: 'Device operation',
  devOperationTitle: 'Set Device Permissions',
  deviceOperationDesc:
    'This permission enables the user to execute device-related operations, such as Device Search, Arm/Disarm, Alarm Output.',
  none: 'None',
  // 布防撤防--除superlive plus外
  cancelDefense: 'Disarm',
  homeDefense: 'Stay',
  outDefense: 'Away',
  defenseDeployment: 'Arm/Disarm',
  oneClickDeployment: 'One-click Arm',
  oneClickDisarm: 'One-click Disarm',
  oneClickRemoval: 'One-click Alarm Clearance',
  deploySuccess: 'Arm Successful',
  disarmSuccess: 'Disarm successfully',
  add: 'Add',
  edit: 'Edit',
  setting: 'Setting',
  all: 'All',
  name: 'Name',
  cameraSensor: 'Link to Camera',
  deleteDeviceConfirm: 'Confirm delete the device?',
  onlySameDevice: 'Only cameras under the same device can be added within the same group',
  pleaseChooseChannel: 'Please choose channel',
  pleaseAddCameraSensor: 'Please add camera/sensor',
  defensiveLinkageItem: 'Disarm linkage item',
  defensiveDesc: 'The selected disarm linkage item in the disarm status is not effective',
  bypassHome: 'Stay Arm Bypass',
  bypassHomeDesc: 'If enabled, the zone will be auto-bypassed during stay-arming',
  ipcSound: 'IPC Sound',
  ipcLight: 'IPC Light',
  pleaseChooseLinkage: 'Please choose disarm linkage item',
  deleteConfirm: 'Confirm delete?',
  pleaseAddGroup: 'Please add a group first',
  groupChannelEmpty: 'No channel has been added to the defense group',
  removalSuccess: 'The clearing alarm command is sent',
  removalFail: 'Alert removal sent fail',
  reqSuccess: 'Success',
  reqFail: 'Fail',
  groupLimit: 'Supports adding up to {limit} groups',
  areaGroup: 'Area group',
  pleaseEnter: 'Please enter',
  groupNoDevice: 'No devices added under grouping',
  // 预置点、巡航线添加--除superlive plus外
  addPoint: 'Add',
  addSuccess: 'Add successfully',
  addFail: 'Add fail',
  editSuccess: 'Edit successfully',
  editFail: 'Edit fail',
  deleteSuccess: 'Delete successfully',
  deleteFail: 'Delete fail',
  pointNameExist: 'Preset Point Name Exist',
  noPointSelect: 'There is no preset to select from. Please create one',
  choosePoint: 'Choose',
  presetPoint: 'Preset Point',
  presetPointName: 'Preset Point Name',
  cruiseLineName: 'Cruise Line Name',
  pleaseEnterPoint: 'Please Enter Preset Point Name',
  pleaseEnterLine: 'Please Enter Cruise Line Name',
  presetPointEmpty: 'The preset point list cannot be empty!',
  lineNameExist: 'Cruise Line Name Exist',
  presetPointLimit: 'The number of preset points cannot exceed {0}!',
  manySecond: '{0} second(s)',
  oneMinute: '1 minute',
  speed: 'Speed',
  holdTime: 'Duration',
  deletePointConfirm: 'Confirm deleting the preset point?',
  pleaseChoosePoint: 'Please Choose Preset Point',
  pleaseChooseSpeed: 'Please Choose Speed',
  pleaseChooseHoldTime: 'Please Choose Duration',
  // line APP绑定解绑
  lineAuthBind: 'line Authority Bingding',
  bindFail: 'Binding fail',
  binding: 'Binding',
  // 1.5.1 superlive plus 住户管理
  householdManagement: 'Buildings & Residents',
  addBuilding: 'Add building',
  buildingName: 'Building Name',
  enterBuildingName: 'Please enter the building name',
  buildingNum: 'Building Number',
  enterBuildingNum: 'Please enter the building number',
  relateDevice: 'Associate Devices',
  roomNum: 'Room quantity',
  room: 'Room',
  addRoom: 'Add Room',
  roomName: 'Room Number',
  enterRoomName: 'Please enter the room number',
  household: 'Residents',
  addRoomMember: 'Add Residents',
  changeSuccessfully: 'Successfully modified',
  email: 'Email',
  enterMemberEmail: 'Please enter email address',
  mobile: 'Phone',
  enterMemberMobile: 'Please enter phone number',
  emailNameError: 'Incorrect email format',
  mobileError: 'Incorrect phone number format',
  emailNameNotEmpty: 'The email cannot be empty',
  mobileNotEmpty: 'The phone number cannot be empty',
  memberInMax: 'The quantity of the residents in this room has reached the upper limit.',
  memberMobileRepeate: 'This phone number already exists in this room.',
  emailRepeate: 'This email already exists in this room.',
  supportDash: 'Only support -, _, and blank spaces as special characters.',
  pageUpdateTitle: 'Update Notice',
  pageUpdateContent: 'The page has been updated. Please refresh the page.',
  ok: 'OK',
  householdDetail: 'Building Details',
  // 1.5.1 superlive max 分享
  chooseSharer: 'Select Recipient',
  shareManagment: 'Share Management',
  mobileNum: 'Phone Number',
  historyShare: 'History Recipient',
  nextStep: 'Next Step',
  choose: 'Select',
  preview: 'Preview',
  shareAuthSetting: 'Set Channel Permissions',
  confirmShare: 'Confirm',
  shareDevice: 'Share Device',
  shareDesc: 'Your device will be shared with this user',
  shareDesc2: 'Based on the permissions you have set, he can view content or control devices',
  remark: 'Remark',
  shareSuccess: 'Sharing Successful',
  shareToUser: 'Your device has been successfully shared with the user',
  myShare: 'My Device',
  otherShare: "Other's Device",
  accept: 'Accept',
  reject: 'Decline',
  toAccept: 'To be Accepted',
  accepted: 'Accepted',
  rejected: 'Declined',
  allAccept: 'All accept',
  acceptSuccMsg: 'Accepted successfully, you can check on the device page of the app',
  acceptFail: 'Accept Failed',
  rejectFail: 'Decline Failed',
  rejectMsg: 'Declined',
  cancelShare: 'Cancel Sharing',
  cancelShareDesc: 'After canceling the sharing, the receiver will no longer be able to view or control your device.',
  notNow: 'Not Now',
  stillCancel: 'Continue',
  initiateShare: 'Initiate Sharing',
  settingSuccess: 'Setting successful',
  deviceNum: '{0} device(s)',
  noShareDevice: 'No shared devices',
  noFoundUser: 'The user was not found',
  notShareSelf: 'Cannot share with oneself',
  tourist: 'Guest',
  andOther: 'and others',
  shareViewFail: 'Unable to view, the device has been unshared',
  messageDetail: 'Message detail',
  agreeShare: 'Agree Share',
  agreed: 'Agreed',
  agree: 'Agree',
  rejectAll: 'Reject All',
  shareApplyDesc:
    '{0} has initiated a device sharing request to you. If allowed, please confirm the channel and permissions.',
  chooseChanAuth: 'Choose channel authority',
  shareOverTenDesc:
    'The configuration parameters of this device have been shared with 10 users and cannot be shared again',
  channelEmpty: 'The channel list is empty',
  msgExpired: 'The message content has expired',
  alarmNotification: 'Alarm notification',
  // 1.5.1 superlive max 托管和转移
  service: 'Services',
  serviceDetail: 'Service Detail',
  site: 'Site',
  changeSiteName: 'Change Site Name',
  cancelHosting: 'Cancel Site Management',
  cancelHostingTip:
    'After canceling authorization, the installer will no longer be able to view or manage the site based on the permissions you granted.',
  cancelHostingError:
    'The installer handed this site to you by sharing. So you cannot cancel their authorization to manage the site. If you wish to cancel it, please contact your installer.',
  cancelDeviceHostingTip:
    'After canceling authorization, you will not be able to grant device permissions to the installer, and the installer will no longer be able to provide remote services for you.',
  devicePermissionApprove: 'Device Management Request',
  enterSiteName: 'Please enter site name',
  deviceHostDetail: 'Device Management Details',
  addInstaller: 'Add Installer',
  enterInstallerEmail: 'Enter the email of the Installer',
  applicationSuccess: 'Sent Successfully',
  applicationInfo: 'The authorization request has been sent to the installer. Please wait for them to process it.',
  remarkPlaceholder: 'Remark',
  hostingDevice: 'Managed Devices',
  noDeviceHosting: 'No device management request',
  sitePermissionApprove: 'Site Management Request',
  sitePermissionCancel: 'Cancel Site Management',
  siteHostingTip:
    'The installer has submitted a request to manage the site on your behalf, This site facilitates the Installer to manage your devices more conveniently. ',
  siteCancelHostingTip:
    'The installer has canceled the management of your site. If your devices require management or maintenance services, you can apply in “Service”.',
  deviceCancelHostingTip:
    'The installer has canceled the management of your device. If your device requires management or maintenance services, you can apply in “Service”.',

  stillUnbind: 'Continue',
  unBindDesc:
    'After unbinding, you will no longer be able to grant device permissions to the installer, and the installer will no longer be able to provide remote services to you.',
  trusteeshipService: 'Authorization Service',
  trusteeshipText: 'Always at Your Service, Anytime, Anywhere',
  usedNumber: '{0} people used it',
  operateText: 'One-Tap Authorization Service, Effortlessly Convenient.',
  fastText: 'Rapid Maintenance Responses to Keep Your Devices Running',
  protectText: 'Secure and Reliable, with Full Control Over Permissions and Actions.',
  immediateTrusteeship: 'Authorize Now',
  introduceText: 'Hi, I am your exclusive service advisor',
  noTrusteeship: 'You currently have no device managed.',
  trustDeviceCount: '{0} device(s) managed',
  trustDeviceNone: 'No devices managed',
  duration: 'Duration',
  siteDescTitle: 'What is a site',
  siteDesc1: "A 'site' facilitates the Installer to manage your devices more conveniently.",
  siteDesc2:
    'If you authorize the Installer to manage your site, they can more conveniently add devices for you and quickly solve the device issues with your authorization.',
  trustPermission: 'Permissions',
  iKnow: 'I got it!',
  deviceRejectMsg: 'Rejected. You may request device management later within the {0}.',
  trustTime: 'Duration',
  permissionSetting: 'Permission Settings',
  settingTime: 'Set Duration',
  trusteeshipSuccess: 'Sent successfully',
  trusteeshipToUser: 'The management request has been sent to the installer',
  trusteeshipDetail: 'Management Details',
  unTrusteeshipDesc:
    'After canceling authorization, you will not be able to grant device permissions to the installer, and the installer will no longer be able to provide remote services for you.',
  transferApply: 'Device Transfer Request',
  deviceTransferDesc:
    'The installer has initiated a device transfer request to you. If accepted, you will be bound to this installer, who can provide device management service for you in the future.',
  bindAcceptTransfer: 'Bind and Accept',
  goUnBind: 'Go to Unbind',
  bindedInstaller: 'Already bound installer',
  transferDesc: 'The installer has initiated a device transfer request to you.',
  bindedInstallerDesc:
    'You already have bound an installer. You can accept this transfer only or unbind the previous installer to bind this one.',
  viewDetail: 'View Details',
  bindInstaller: 'Bind Installer',
  bindInstallerDesc:
    'You should bound an installer before authorizing device. Please show or share the QR code to the installer for binding.',
  noTrusteeshipDevice: 'No devices under management',
  deviceTrusteeship: 'Device Management',
  chooseTrusteeshipDevice: 'Select Devices for Management',
  deviceTransfer: 'Device Transfer',
  transferDeviceCount: 'You currently have {0} device(s) being transferred',
  transferDevice: 'Transfer Device',
  transferSuccess: 'Transfer successful',
  rejectTransfer: 'Decline',
  rejectDeviceTransfer: 'Reject device transfer',
  rejectTransfetDesc: 'After declining the transfer, you will not be able to view or control the device.',
  waitTrust: 'To be managed',
  waitTrustReceived: 'To be accepted',
  devicePermissionCancel: 'Cancel device Management',
  duringTrust: 'Under Management',
  residueTime: 'Remaining Time',
  duringTrsutResidueTime: 'Managed Remaining Time',
  manyMinutesEn2: '{0} minute(s)',
  manySeconds: '{0}s',
  manySecondsEn: '{0} second(s)',
  refresh: 'Refresh',
  netErrText: 'Network exception. Click to retry',
  cancelled: 'Canceled',
  transferAcceptedDesc: 'Transfer successful',
  transferRejectedDesc: 'Declined',
  applyExpiredDesc: 'The request has expired. Please ask for a resend',
  applyCancelledDesc: 'Canceled',
  noDeviceTransfer: 'No device transfer request',
  // 设备交付
  skip: 'Skip',
  selectDevice: 'Select device',
  ensure: 'Confirm',
  waitingTips: 'Adding device, please do not leave the current page',
  finished: 'Finish',
  successAdd: 'Successfully added {num} devices',
  failAdd: '{num} devices failed to add',
  nChannels: '{n} channels',
  deviceName: 'Device name',
  open: 'open',
  close: 'close',
  adding: 'Adding',
  addToSite: 'Add to account',
  bindByOther: 'The device has been added by user {name}',
  enterVerificationCode: 'Enter verification code',
  enterDevVerificationCode: 'Please enter verification/security code',
  noCode: 'Failed to obtain security code',
  input: 'Enter ',
  codeTips1: 'You can follow the steps below to obtain the device verification code/security code',
  codeTips2: '1. Enter the verification code/security code number on the interface:',
  codeTips3: 'Device side: Main menu-Settings-Network-NAT',
  codeTips4: 'Web page: Main menu-Function panel-Network-NAT',
  codeTips5:
    '2. The device verification code/security code is located on the right side of the device QR code. See the figure below for details:',
  emptyDevice: 'No device',

  // 1.5.1 superlive plus 人脸库管理
  targetFaceManagement: 'Face Database',
  targetName: 'Name',
  targetType: 'Type',
  cardId: 'Card Number',
  strangerList: 'Visitor',
  whiteList: 'Allow List',
  blackList: 'Block List',
  admin: 'Admin',
  filter: 'Filter',
  searchTargetFace: 'Enter name',
  personType: 'User Type',
  addPerson: 'Add User',
  personFace: 'Face',
  floor: 'Floor',
  verificationMethod: 'Unlocking Mode',
  lockPermission: 'Door Lock',
  startTime: 'Start Time',
  endTime: 'End Time',
  gender: 'Gender',
  age: 'Age',
  telephone: 'Mobile',
  termOfValidity: 'Term of validity',
  foreverValid: 'Always Valid',
  custom: 'Custom',
  jobNumber: 'ID Number',
  male: 'Male',
  female: 'Female',
  Password: 'PIN Code',
  FaceMatch: 'Face Comparison',
  SwipingCard: 'Swiping Card',
  MatchAndPass: 'Face Comparison + PIN Code',
  MatchAndCard: 'Face Comparison + Swiping Card',
  MatchorCard: 'Face Comparison or Swiping Card',
  AllType: 'Face Comparison or PIN Code or Swiping Card',
  doorLock: 'Door Lock',
  'personFace.uploadTip': 'Please upload a facial image',
  'personFace.sizeTip': 'The image size cannot exceed 200k',
  ageRangeTip: 'The age range is {min}~{max}',
  floorRangeTip: 'The floor range is {min}~{max}',
  roomRangeTip: 'The room number range is {min}~{max}',
  cardIdTip: 'The number of card numbers cannot exceed {max}!',
  passwordRangeTip: 'The PIN code length range is {min}~{max}',
  passwordLengthTip: 'The PIN code length is {length}',
  timeRangeTip: 'The end time must be later than the start time!',
  doorLockTip: 'Select at least one door lock',
  jobNumberTip: 'Please enter the ID Number',
  cardIdsTip: 'Please enter the card number',
  confirmDeletePerson: 'Confirm the deletion of this user?',
  cardIdExist: 'The card number already exists',
  faceMatchErrorCode: {
    '-2': 'Parameter error!',
    '-3': 'The image does not meet the requirements',
    '-5': 'The number of users has reached its maximum limit.',
    '-6': 'The image size exceeds the limit!',
    '-11': 'Unsupported image format',
    '-12': 'Image data is corrupted',
    '-16': 'Import failed',
    '-18': 'Image size exceeds the limit',

    '-19': 'Failed to update the feature library record',
    '-20': 'Failed to add the feature library record',
    '-21': 'Failed to extract the image features',
    '-22': 'Failed to save base image',
    '-23': 'Failed to save user information',
    '-24': 'Failed to save personnel information',
    '-25': 'The same face already exists',
    '-26': 'Unknown feature library operation error',

    '-30': 'Unlocking mode parameter error',
    '-31': 'The PIN code cannot be repeated',
    '-32': 'The PIN code cannot be empty',
    '-33': 'The ID number cannot be empty',
    '-34': 'The card number cannot be empty',
    '-35': 'No face image',
    499: 'Parameter verification failed'
  },
  // 1.5.1 superlive max云存储
  immediateBuy: 'Buy Now',
  secureAndQuick: 'Safe and Reassuring, Convenient and Quick.',
  purchaseRecords: 'Purchase Records',
  cloudStorageInUse: 'Using Cloud Storage',
  serviceBought: '{0} channel(s) in service',
  // 1.5.1 superlive max布撤防
  linkCameraSensor: 'Link to Camera/Sensor',
  defenseAreaSetting: 'Defense Area Setting',
  enterGroupName: 'Please enter group name',
  createGroup: 'Create group',
  msgPushSwitch: 'Push',
  alarmOut: 'Alarm out',
  saveModify: 'Save modify',
  addedTo: 'Added to {0}',
  pleaseEnterName: 'Please enter name',
  noGroupDesc: 'No groups have been added. You can add them now',
  createSuccess: 'Create successfully',
  createFail: 'Create fail',
  saveSuccess: 'Save successfully',
  saveFail: 'Save fail',
  selectAll: 'Select all',
  cancelSelectAll: 'Cancel select all',
  // 云后台云升级
  repeatedlyRestart: 'Repeatedly restart',
  versionUnchanged: 'Version unchanged',
  versionException: 'Version exception',
  // max新的设备转移和托管权限申请
  transferManagApply: 'Transfer and Permission Request',
  stillReject: 'Still reject',
  trusteeshipAuthApply: 'Permission Request',
  agreeTrusteeshipDesc: 'Approved. Management permissions can be modified later within the {0}.',
  rejectTrusteeshipDesc: 'Rejected. You may request device management later within the {0}.',
  transferApplyTip: 'The installer requests to manage the site ({0}) and its devices.',
  // BA-4491 用户反馈 满意度收集
  userFeedback: 'User Feedback',
  feedBackPlaceholder1:
    'Please write down your feedback on the use of the app so that we can provide you with a better product experience in the future.',
  feedBackPlaceholder2: 'Please write down your feedback on the use of the app',
  starOne: 'Very dissatisfied, did not achieve the expected effect',
  starTwo: 'Not satisfied, all needs have not been fully met',
  starThree: 'Generally, the service and experience are basically satisfactory, but there is room for improvement',
  starFour: 'Satisfied, with a good experience and the ability to achieve perfection',
  starFive: 'Very satisfied, exceeded expectations, excellent experience',
  feedBackSuccess: 'Feedback successful',
  feedBackFail: 'Feedback failed',
  feedBackInfo:
    'In order to provide you with a better experience in the future, we would greatly appreciate your feedback and valuable opinions.',
  submit: 'Submit',
  feedBackFinish: 'You can continue to evaluate in {me}-{userFeedback} in the future',
  me: 'Me',
  // 重置密码
  resetDevPwd: 'Reset device password',
  resetPwdTips: 'The installer reminds you to reset the device password',
  modifyDevPwd: 'Change device password',
  pwdTips:
    'Password length is 8-16 characters, including at least two types of numbers, letters (case sensitive), and characters',
  confirmNewPassword: 'Confirm new password',
  emptyPsw: 'Password cannot be empty',
  emptyOldPwd: 'Old password cannot be empty',
  setPwdTips: 'Changing password requires verifying the old password. Please enter the old password first',
  oldPwd: 'Old password',
  enterOldPwd: 'Please enter old password',
  newPwd: 'Password',
  enterNewPwd: 'Please enter a new password',
  confirmPwd: 'Confirm password',
  enterConfirmPwd: 'Please enter the confirmation new password',
  notMatchPsw: 'The passwords entered twice do not match. Please re-enter',
  updateTips: 'You can modify your device name for future reference',
  updateWarnTips: 'Do not support Special characters such as {0}',
  pleaseEnterDevName: 'Please enter the device name',
  updateDevName: 'Modify device name',
  errRenameTips: 'Incorrect input of device username or password',
  mediumPwd:
    '1. The length is 8-16 characters.\n 2. It contains two or more of numbers, lowercase letters, uppercase letters, and symbols',
  strongPwd:
    '1. The length is 8-16 characters.\n 2. It contains three or more of numbers, lowercase letters, uppercase letters, and symbols',
  strongerPwd:
    '1. The length is 9-16 characters.\n2. It includes numbers, lowercase letters, uppercase letters, and symbols',
  mediumPwdTips:
    'The password length is 8-16 characters, including two or more of numbers, lowercase letters, uppercase letters, and symbols',
  strongPwdTips:
    'The password length is 8-16 characters, including three or more of numbers, lowercase letters, uppercase letters, and symbols',
  strongerPwdTips:
    'The password length is 9-16 characters, including numbers, lowercase letters, uppercase letters, and symbols',
  // max 设备分享交付
  deviceShareDeliveryTips:
    'The installer shares the device(s) with you and can always configure or stop sharing after you accept.',
  rejectShareTitle: 'Decline Sharing',
  rejectShareTips: 'If you decline device sharing, you cannot view or control the device.',
  shareDeliceryTitle: 'Device Handover Request',
  shareChangeSiteTitle: 'Device Move Request',
  shareChangeSiteTip1: 'The installer requests to move device(s) from original site',
  shareChangeSiteTip2: 'to target site',
  emphasizeStr: ' "{name}" ',
  changeSite: 'Move Device(s)',
  changeSiteWaitingTip: 'Device is moving… Please check back later.',
  moveSuccessfully: 'Move successfully',
  moveFailed: 'Failed to move',
  shareDeliceryCancelTitle: 'Cancel Sharing',
  shareCancelTips: 'The installer has canceled device sharing with you. Contact them if you have questions.',
  // superlive max 2.2  云盘卡片
  gift: 'Gift',
  cloudDiskUse: 'Cloud Disk In Use',
  capacity: 'Capacity',
  totalFiles: '{0} files in total',
  expirationTime: 'Expiration Time',
  // superlive max 2.2  云存储卡片
  purchasedChannel: 'Purchased service channel',
  errorCode: {
    536870947: 'Username does not exist',
    536870948: 'Username or password error',
    536871060: 'Operation failure, please check the device status', //'Device busy, please try again later',
    10000: 'Failed to connect device',
    550: 'Request timeout', //app返回的超时
    12344: 'Network connection failed',
    12345: 'Network connection timeout',
    23024: 'The payment card provided has expired',
    23025: 'The transaction has been rejected due to violation',
    400: 'Parameter error',
    404: 'The requested resource (web page, etc.) does not exist',
    500: 'System exception',
    502: 'Server request failed',
    503: 'Server exception',
    504: 'Server request timeout',
    32018: 'Data does not exist',
    32022:
      '{0} device and installer are not in the same country/region, so the device management service is not supported',
    536871039: 'Invalid parameter',
    536870943: 'Invalid parameter',
    536870945: 'Operation failure, please check the device status', //'Device busy, please try again later',
    536871017: 'Operation failure, please check the device status', // 版本不匹配
    536871082: 'Operation failure, please check the device status', //'无新版本',
    536871083: 'Operation failure, please check the device status', //'云升级版本不存在'
    536870940: 'Operation failure, please check the device status', //云升级未开启
    536870934: 'Operation failure, please check the device status',
    536870975: 'Operation failure, please check the device status',
    536871030: 'No HDD',
    1000: 'Parameter error',
    1005: 'Image verification code error',
    1007: 'Picture verification code is required',
    1008: 'The verification code has expired',
    1009: 'Verification code error',
    1011: 'The parameter is not filled in correctly!',
    1012: 'API not recognized',
    1013: 'The verification code failed to send',
    1015: 'The user already exists',
    1027: 'Please enter the correct device serial number/security code',
    1028: 'The camera is enabled or disabled',
    4500: 'Parameter error',
    5000: 'Sorry, you do not have permission to perform this operation',
    5001: 'The current user has no permission',
    6000: 'The current business status does not support this operation',
    6001: 'Too frequent operation',
    7000: 'Parameter error',
    7001: 'The user does not exist',
    7002: 'Old password error!',
    7003: 'Token error!',
    7004: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7005: 'Invalid signature',
    7006: 'Mobile number already exists',
    7007: 'The user is locked, please contact the administrator to unlock',
    7009: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7010: 'The administrator account is not activated',
    7011: 'Account not activated',
    7019: 'The username already exists',
    7021: 'Deletion failed! Please clear all hosts under this host group first',
    7023: 'The mailbox has been bound',
    7028: 'The template has been used in the project and cannot be deleted!',
    7029: 'The template name already exists!',
    7030: 'The data already exists!',
    7032: 'The firmware package already exists!',
    7034: 'The firmware package has been released and cannot be deleted!',
    7042: 'There are other tasks in the startup state',
    7043: 'The task has not been approved!',
    7044: 'Operation failed. There are no devices eligible for upgrade!',
    7045: 'The task is not approved!',
    7056: 'This version has been included in the supporting compatibility management and cannot be deleted!',
    7057: 'Issuing document cannot be blank!',
    7061: 'Correction failed, cannot create correction again!',
    7066: 'The customer code already exists!',
    7068: 'The customer code does not exist!',
    7069: 'Too much data, please narrow the scope and search again!',
    7081: 'Import failed!',
    7082: 'Export failed!',
    7084: 'The customer country code already exists',
    7086: 'The operation is refused due to system exception',
    7087: 'The product already exists!',
    7088: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7090: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7093: 'Image and text information is not configured!',
    7094: 'The service terms information does not exist!',
    9000: 'System exception!',
    9001: 'The protocol version is too low. The old version is no longer compatible and needs to be upgraded',
    9002: 'Protocol version error, unrecognized version field or error message',
    9003: 'Failed to send verification code',
    9004: 'Database operation failed',
    9005: 'The data does not exist',
    9006: 'The data already exists',
    9007: 'The data to be viewed does not exist',
    9008: 'The data does not exist',
    9009: 'Data exception',
    9500: 'System exception!',
    10001: 'System exception!',
    20021: 'This email has been used',
    20024: 'The account has been activated',
    20030: 'The link has expired',
    33001: 'No permission to operate this device',
    33002: 'No permission to operate this site',
    33003: 'The site does not exist',
    33004: 'The length of the device name must be between 0 and 32',
    33010: 'The device already exists',
    7072: 'The device already exists',
    // 托管错误码
    32019: 'Operation failed',
    32021: 'Data does not exist',
    // 安装商转移
    34006: 'Device transfer does not exist',
    34007: 'Transfers are only allowed from the same user.',
    7040: 'Device does not exist or is not online',
    7065: 'Channel has already been shared',
    // ipc 云升级错误码
    ipc: {
      499: 'Unknown error', //未知错误
      612: 'Operation failure, please check the device status',
      730: 'Operation failure, please check the device status', //检查新版本信息时，无新版本信息
      731: 'Operation failure, please check the device status', //云升级功能未使能  升级guid错误
      732: 'Operation failure, please check the device status', //升级任务已存在
      735: 'Operation failure, please check the device status',
      101001: 'Unable to connect the device, please check the device status',
      99006: 'Network Connection Unavailable',
      604: 'Unable to connect the device, please check the device status',
      536871049: 'Cloud upgrade exception',
      536870931: 'Connection disconnected',
      733: 'Operation failure, please check the device status',
      734: 'Operation failure, please check the device status',
      736: 'Operation failure, please check the device status',
      737: 'Operation failure, please check the device status',
      738: 'Operation failure, please check the device status',
      739: 'Operation failure, please check the device status',
      740: 'Operation failure, please check the device status',
      130001: 'Unable to connect the device, please check the device status',
      101003: 'Unable to connect the device, please check the device status'
    },
    101001: 'Unable to connect the device, please check the device status',
    99006: 'Network Connection Unavailable',
    604: 'Unable to connect the device, please check the device status',
    536871049: 'Cloud upgrade exception',
    536870931: 'Connection disconnected',
    733: 'Operation failure, please check the device status',
    734: 'Operation failure, please check the device status',
    736: 'Operation failure, please check the device status',
    737: 'Operation failure, please check the device status',
    738: 'Operation failure, please check the device status',
    739: 'Operation failure, please check the device status',
    740: 'Operation failure, please check the device status',
    130001: 'Unable to connect the device, please check the device status',
    101003: 'Unable to connect the device, please check the device status',
    // Line App绑定
    34003: 'The state information is incorrect',
    34001: 'This account has been bound.',
    34004: 'Authorization failed.',
    34005: 'Operation Failed: The authorization has expired. Please obtain it again.',
    22022:
      'This LINE account is already linked to another HiviewPlus user. Please use a different LINE account, or contact the original linked user to unlink it.',
    // 1.5.1 布防撤防 -除superlive plus外
    33601: 'repetitive device!',
    33602: 'Exceeded the maximum number of groups!',
    33603: 'Operation failed!',
    20070: 'Failed to invite this user because you are in different data centers.',
    20071: 'Failed to invite this user because you are in different data centers.',
    // 1.5.1 superlive plus 住户管理
    34021: 'The quantity of the building has reached the upper limit.',
    34022: 'The building name already exists',
    34023: 'The Building number already exists',
    34024: 'Delete failed. Please first delete the rooms and devices in this building.',
    34025: 'Associate failed. This device has been associated to another building.',
    34026: 'The quantity of the devices in this building has reached the upper limit.',
    34027: 'Operation failed. This building has been deleted.',
    34028: 'This room number already exists.',
    34029: 'The quantity of the room in this building has reached the upper limit.',
    34030: 'The quantity of the residents in this room has reached the upper limit.',
    34031: 'Operation failed. This room has been deleted.',
    34033: 'Delete failed. Please first delete the residents in this room.',
    34035: 'The phone number already exists in this room.',
    34036: 'This email already exists in this room.',
    34037: 'Operation failed. This resident has been deleted.',
    // 托管
    34203: 'This Installer does not exist.',
    34204:
      'The management status of the device has been updated. Please refresh the pages to synchronize with the latest status.',
    34205:
      'The status of the request has been updated. Please refresh the pages to synchronize with the latest status.',
    34206:
      'The status of the request has been updated. Please refresh the pages to synchronize with the latest status.',
    34207: 'The status of the site has been updated. Please refresh the pages to synchronize with the latest status.',
    34208: 'The status of the site has been updated. Please refresh the pages to synchronize with the latest status.',
    34209:
      'The management status of the device has been updated. Please refresh the pages to synchronize with the latest status.',
    // superlive max 分享
    10004: 'Parameter error',
    21109: 'The device has exited sharing',
    // 云后台-云升级
    33681: 'The device does not support this upgrade method',
    33682: 'The channel does not exist',
    33683: 'Channel offline',
    7046: 'Reached version detection limit',

    // 绑定设备错误码
    7048: 'Device does not exist.',
    7062: 'The device is offline',
    7063: 'The device has been bound by someone else',
    100008: 'The device already exists',
    7085: 'The user cannot bind the device',
    33016: 'Binding device...',
    7071: 'The device is not enabled for this cloud business',
    // 重置密码错误码
    100003: 'The specified device was not found',
    101007: 'The specified device was not found',
    211: 'User is locked',
    215: 'System busy',
    217: 'connection timed out',
    130000: 'Unable to connect the device, please check the device status'
  }
}
