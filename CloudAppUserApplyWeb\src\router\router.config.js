/**
 * 基础路由
 * @type { *[] }
 * meta.hideTopArea 此页面是否隐藏头部安全区
 * meta.hideBottomArea 此页面是否隐藏底部安全区
 */

const layout = () => import('@/layout/index.vue')

const notFoundCompnent = () => import('@/views/404')

export const constantRouterMap = [
  {
    path: '/cloud',
    component: layout,
    redirect: '/cloud/upgrade',
    children: [
      {
        path: 'ipcUpgrade',
        name: 'ipcUpgrade',
        component: () => import('@/views/cloud/IpcUpgrade'),
        meta: { title: 'ipcUpgrade', keepAlive: false }
      },
      {
        path: 'upgrade',
        name: 'upgrade',
        component: () => import('@/views/cloud/Upgrade'),
        meta: { title: 'upgrade', keepAlive: false }
      },
      {
        path: 'cloudUpgrade',
        name: 'upgrade',
        component: () => import('@/views/cloud/CloudUpgrade'),
        meta: { title: 'upgrade', keepAlive: false }
      },
      {
        path: 'cloudIpcIUpgrade',
        name: 'ipcUpgrade',
        component: () => import('@/views/cloud/CloudIpcUpgrade'),
        meta: { title: 'ipcUpgrade', keepAlive: false }
      }
    ]
  },
  /**  plus 云升级 */
  {
    path: '/cloudUpgrade',
    component: layout,
    redirect: '/cloudUpgrade/upgrade',
    children: [
      {
        path: 'ipcUpgrade',
        name: 'ipcUpgrade',
        component: () => import('@/views/cloud-upgrade/IpcUpgrade'),
        meta: { title: 'ipcUpgrade', keepAlive: false }
      },
      {
        path: 'upgrade',
        name: 'upgrade',
        component: () => import('@/views/cloud-upgrade/Upgrade'),
        meta: { title: 'upgrade', keepAlive: false }
      },
      {
        path: 'cloudUpgrade',
        name: 'upgrade',
        component: () => import('@/views/cloud-upgrade/CloudUpgrade'),
        meta: { title: 'upgrade', keepAlive: false }
      },
      {
        path: 'cloudIpcIUpgrade',
        name: 'ipcUpgrade',
        component: () => import('@/views/cloud-upgrade/CloudIpcUpgrade'),
        meta: { title: 'ipcUpgrade', keepAlive: false }
      }
    ]
  },
  // 设备托管
  {
    path: '/trusteeship',
    component: layout,
    redirect: '/trusteeship/myInstaller',
    children: [
      {
        path: 'myInstaller',
        name: 'myInstaller',
        component: () => import(/* webpackChunkName: "Trusteeship" */ '@/views/trusteeship/MyInstaller'),
        meta: { title: 'myInstaller', keepAlive: false }
      },
      {
        path: 'chooseDevice/:userType?',
        name: 'chooseDevice',
        component: () => import(/* webpackChunkName: "Trusteeship" */ '@/views/trusteeship/ChooseDevice'),
        meta: { title: 'chooseDevice', keepAlive: false }
      },
      {
        path: 'deviceDetails/:id/:userType?',
        name: 'deviceDetails',
        component: () => import(/* webpackChunkName: "Trusteeship" */ '@/views/trusteeship/DeviceDetails'),
        meta: { title: 'deviceDetails', keepAlive: false }
      },
      {
        path: 'permission/:index/:from/:id?',
        name: 'permission',
        component: () => import(/* webpackChunkName: "Trusteeship" */ '@/views/trusteeship/Permission'),
        meta: { title: 'permission', keepAlive: false }
      },
      {
        path: 'validity/:index/:from/:id?',
        name: 'validity',
        component: () => import(/* webpackChunkName: "Trusteeship" */ '@/views/trusteeship/Validity'),
        meta: { title: 'permissionValidity', keepAlive: false }
      }
    ]
  },
  // 转移请求
  {
    path: '/transfer',
    component: () => import('@/views/transfer/TransferRequest'),
    meta: { title: 'transferRequest', keepAlive: false }
  },
  // 布防撤防
  {
    path: '/defense',
    component: layout,
    redirect: '/defense/defenseDeployment',
    children: [
      {
        path: 'defenseDeployment',
        name: 'defenseDeployment',
        component: () => import(/* webpackChunkName: "defense" */ '@/views/defense/DefenseDeployment'),
        meta: { title: 'defenseDeployment', keepAlive: false }
      },
      {
        path: 'addEditDefense/:id?',
        name: 'addEditDefense',
        component: () => import(/* webpackChunkName: "defense" */ '@/views/defense/AddEditDefense'),
        meta: { title: 'add', keepAlive: false }
      },
      {
        path: 'addChannel',
        name: 'addChannel',
        component: () => import(/* webpackChunkName: "defense" */ '@/views/defense/AddChannel'),
        meta: { title: 'add', keepAlive: false }
      },
      {
        path: 'groupSetting',
        name: 'groupSetting',
        component: () => import(/* webpackChunkName: "defense" */ '@/views/defense/GroupSetting'),
        meta: { title: 'setting', keepAlive: false }
      },
      {
        path: 'ipcSetting',
        name: 'ipcSetting',
        component: () => import(/* webpackChunkName: "defense" */ '@/views/defense/IpcSetting'),
        meta: { title: 'ipcSetting', keepAlive: false }
      },
      {
        // path: 'ipcLinkage/:index/:from/:id?',
        path: 'ipcLinkage',
        name: 'ipcLinkage',
        component: () => import(/* webpackChunkName: "defense" */ '@/views/defense/IpcLinkage'),
        meta: { title: 'ipcLinkage', keepAlive: false }
      }
    ]
  },
  // 预置点
  {
    path: '/presetPoint',
    component: layout,
    redirect: '/presetPoint/addEditPoint',
    children: [
      {
        path: 'addEditPoint',
        name: 'addEditPoint',
        component: () => import(/* webpackChunkName: "presetPoint" */ '@/views/preset-point/AddEditPoint'),
        meta: { title: 'addPoint', keepAlive: false }
      },
      {
        path: 'choosePoint',
        name: 'choosePoint',
        component: () => import(/* webpackChunkName: "presetPoint" */ '@/views/preset-point/ChoosePoint'),
        meta: { title: 'choosePoint', keepAlive: false }
      }
    ]
  },
  // 巡航线
  {
    path: '/cruiseLine',
    component: layout,
    redirect: '/cruiseLine/addEditLine',
    children: [
      {
        path: 'addEditLine/:id?',
        name: 'addEditLine',
        component: () => import(/* webpackChunkName: "cruiseLine" */ '@/views/cruise-line/AddEditLine'),
        meta: { title: 'addPoint', keepAlive: false }
      },
      {
        path: 'addEditCruisePoint/:type?',
        name: 'addEditCruisePoint',
        component: () => import(/* webpackChunkName: "cruiseLine" */ '@/views/cruise-line/AddEditCruisePoint'),
        meta: { title: 'addPoint', keepAlive: false }
      },
      {
        path: 'chooseCruisePoint',
        name: 'chooseCruisePoint',
        component: () => import(/* webpackChunkName: "cruiseLine" */ '@/views/cruise-line/ChooseCruisePoint'),
        meta: { title: 'choosePoint', keepAlive: false }
      },
      {
        path: 'chooseSpeed',
        name: 'chooseSpeed',
        component: () => import(/* webpackChunkName: "cruiseLine" */ '@/views/cruise-line/ChooseSpeed'),
        meta: { title: 'speed', keepAlive: false }
      },
      {
        path: 'chooseHoldTime',
        name: 'chooseHoldTime',
        component: () => import(/* webpackChunkName: "cruiseLine" */ '@/views/cruise-line/ChooseHoldTime'),
        meta: { title: 'holdTime', keepAlive: false }
      }
    ]
  },
  // line APP绑定/解绑页
  {
    path: '/lineAuthBind',
    component: () => import('@/views/line-bind/LineAuthBind'),
    meta: { title: 'lineAuthBind', keepAlive: false }
  },
  {
    path: '/device',
    name: 'device',
    component: layout,
    redirect: '/device/addDeviceToSite',
    children: [
      {
        path: 'addDeviceToSite',
        name: 'addDeviceToSite',
        component: () => import('@/views/add/AddDevice'),
        meta: { title: 'addToSite', keepAlive: false }
      },

      {
        path: 'waitingAdd',
        name: 'waitingAdd',
        component: () => import('@/views/add/WaitingToAdd'),
        meta: { title: 'deviceInit', keepAlive: false }
      },
      {
        path: 'updateDevName',
        name: 'updateDevName',
        component: () => import('@/views/add/UpdateDevName'),
        meta: { title: 'updateDevName', keepAlive: false }
      },
      // 重置设备密码
      {
        path: 'resetDevPwd/:sn',
        name: 'resetDevPwd',
        component: () => import('@/views/device/ResetDevPwd'),
        meta: { title: 'resetDevPwd', keepAlive: false }
      },
      // 修改密码
      {
        path: 'modifyDevPwd',
        name: 'modifyDevPwd',
        component: () => import('@/views/device/ModifyDevPwd'),
        meta: { title: 'modifyDevPwd' }
      }
    ]
  },
  {
    path: '/payRes',
    component: () => import('@/views/cloud/CloudStoragePayRes'),
    meta: { title: 'cloudStorage', keepAlive: false }
  },
  // superlive plus 住户管理
  {
    path: '/household',
    component: layout,
    redirect: '/household/management',
    children: [
      {
        path: 'management',
        name: 'householdManagement',
        component: () => import('@/views/household-management/index'),
        meta: { title: 'householdManagement', keepAlive: false }
      },
      // 添加/编辑 楼栋
      {
        path: 'addBuilding',
        component: () => import('@/views/household-management/AddBuilding'),
        meta: { title: 'addBuilding', keepAlive: false }
      },
      // 关联设备
      {
        path: 'chooseDevice',
        component: () => import('@/views/household-management/ChooseDevice'),
        meta: { title: 'relateDevice', keepAlive: false }
      },
      // 添加房间
      {
        path: 'addRoom',
        component: () => import('@/views/household-management/AddRoom'),
        meta: { title: 'addRoom', keepAlive: false }
      },
      // 添加住户
      {
        path: 'addRoomMember',
        component: () => import('@/views/household-management/AddRoomMember'),
        meta: { title: 'addRoomMember', keepAlive: false }
      }
    ]
  },
  // superlive max 分享
  {
    path: '/share',
    component: layout,
    redirect: '/share/chooseSharer',
    children: [
      // 选择分享人
      {
        path: 'chooseSharer',
        name: 'chooseSharer',
        component: () => import('@/views/share/ChooseSharer'),
        meta: { title: 'chooseSharer', keepAlive: false }
      },
      // 扫码分享结果
      {
        path: 'scanSharer',
        name: 'scanSharer',
        component: () => import('@/views/share/ScanSharer'),
        meta: { title: 'chooseSharer', keepAlive: false }
      },
      // 选择分享设备
      {
        path: 'chooseDevice',
        name: 'chooseDevice',
        component: () => import('@/views/share/ChooseDevice'),
        meta: { title: 'choose', keepAlive: false }
      },
      // 选择分享设备的权限
      {
        path: 'changeCapability',
        name: 'changeCapability',
        component: () => import('@/views/share/ChangeCapability'),
        meta: { title: 'changeCapability', keepAlive: false }
      },
      // 确认分享
      {
        path: 'confirmShare',
        name: 'confirmShare',
        component: () => import('@/views/share/ConfirmShare'),
        meta: { title: 'confirmShare', keepAlive: false }
      },
      // 分享成功
      {
        path: 'shareSuccess',
        name: 'shareSuccess',
        component: () => import('@/views/share/ShareSuccess'),
        meta: { title: 'confirmShare', keepAlive: false }
      },
      // 分享管理 -- 我的分享/他人分享
      {
        path: 'shareManagment',
        name: 'shareManagment',
        component: () => import('@/views/share/ShareManagment'),
        meta: { title: 'shareManagment', keepAlive: false }
      },
      // 分享管理 -- 编辑我的分享通道
      {
        path: 'shareDetail',
        name: 'shareDetail',
        component: () => import('@/views/share/ShareDetail'),
        meta: { title: 'shareDetail', keepAlive: false }
      },
      // 分享申请
      {
        path: 'shareApply',
        name: 'shareApply',
        component: () => import('@/views/share/ShareApply'),
        meta: { title: 'messageDetail', keepAlive: false }
      }
    ]
  },
  // superlive max 托管 跟superlive cloud 托管分开
  {
    path: '/max/hosting',
    component: layout,
    redirect: '/max/hosting/service',
    children: [
      // 设备托管服务
      {
        path: 'service',
        name: 'maxHostingService',
        component: () => import('@/views/max-hosting/ServiceView'),
        meta: { title: 'service', keepAlive: false, hideTopArea: false, hideBottomArea: true }
      },
      // 服务详情
      {
        path: 'service-detail/:id',
        name: 'maxHostingServiceDetail',
        component: () => import('@/views/max-hosting/ServiceDetail'),
        meta: { title: 'serviceDetail', keepAlive: false }
      },
      // 设备托管详情
      {
        path: 'device-detail/:userId/:deviceId',
        name: 'maxHostingDeviceDetail',
        component: () => import('@/views/max-hosting/DeviceHostingDetail'),
        meta: { title: 'deviceHostDetail', keepAlive: false }
      },
      // 添加安装商
      {
        path: 'installer/add',
        name: 'maxHostingInstallerAdd',
        component: () => import('@/views/max-hosting/AddInstaller'),
        meta: { title: 'addInstaller', keepAlive: false }
      },
      // 选择设备
      {
        path: 'device/select',
        name: 'maxHostingDeviceSelect',
        component: () => import('@/views/max-hosting/SelectHostingDevice'),
        meta: { title: 'chooseDevice', keepAlive: false }
      },
      // 申请成功
      {
        path: 'application/success',
        name: 'maxHostingApplicationSuccess',
        component: () => import('@/views/max-hosting/ApplicationSuccess'),
        meta: { title: 'reqSuccess', keepAlive: false }
      },
      // 站点或者设备托管权限申请
      {
        path: 'approve/:orderType/:id',
        name: 'maxHostingApprove',
        component: () => import('@/views/max-hosting/DeviceHostingApprove'),
        meta: { title: 'devicePermissionApprove', keepAlive: false }
      }
    ]
  },
  // superlive max设备转移申请详情
  {
    path: '/maxTransfer',
    component: layout,
    redirect: '/maxTransfer/transferApplay',
    children: [
      // 转移申请
      {
        path: 'transferApplay',
        name: 'transferApplay',
        component: () => import('@/views/transfer/TransferApplay'),
        meta: { title: 'transferApply', keepAlive: false }
      },
      {
        path: 'transferManagApply',
        name: 'transferManagApply',
        component: () => import('@/views/transfer/TransferManagApply'),
        meta: { title: 'transferManagApply', keepAlive: false }
      },
      // 分享交付申请
      {
        path: 'shareApprove/:orderId/:siteId',
        name: 'deviceShareApprove',
        component: () => import('@/views/transfer/DeviceShareApprove'),
        meta: { title: 'deviceShareApprove', keepAlive: false }
      },
      // 设备移动站点申请
      {
        path: 'moveApprove/:orderId',
        name: 'deviceChangeSiteApprove',
        component: () => import('@/views/transfer/DeviceChangeSiteApprove'),
        meta: { title: 'deviceChangeSiteApprove', keepAlive: false }
      },
      // 设备访问提醒
      {
        path: 'deviceVisit',
        name: 'deviceVisit',
        component: () => import('@/views/transfer/DeviceVisit'),
        meta: { title: 'deviceVisit', keepAlive: false }
      }
    ]
  },
  // superlive plus 人脸库管理
  {
    path: '/targetFace',
    component: layout,
    redirect: '/targetFace/list',
    children: [
      // 列表
      {
        path: 'list',
        name: 'targetFaceList',
        component: () => import('@/views/target-face/index'),
        meta: { title: 'targetFaceManagement', keepAlive: false }
      },
      {
        path: 'editCardId',
        name: 'targetFaceCardId',
        component: () => import('@/views/target-face/EditCardId'),
        meta: { title: 'targetFaceManagement', keepAlive: false }
      },
      // 详情
      {
        path: 'detail',
        name: 'targetFaceDetail',
        component: () => import('@/views/target-face/TargetDetail'),
        meta: { title: 'details', keepAlive: false }
      }
    ]
  },
  // superlive max 布撤防
  {
    path: '/maxDefense',
    component: layout,
    redirect: '/maxDefense/defenseDeployment',
    children: [
      {
        path: 'defenseDeployment',
        name: 'maxDefenseDeployment',
        component: () => import('@/views/max-defense/DefenseDeployment'),
        meta: { title: 'defenseDeployment', keepAlive: false }
      },
      {
        path: 'addEditDefense/:id?',
        name: 'maxAddEditDefense',
        component: () => import('@/views/max-defense/AddEditDefense'),
        meta: { title: 'add', keepAlive: false }
      },
      {
        path: 'addChannel',
        name: 'maxAddChannel',
        component: () => import('@/views/max-defense/AddChannel'),
        meta: { title: 'add', keepAlive: false }
      },
      {
        path: 'ipcSetting',
        name: 'maxIpcSetting',
        component: () => import('@/views/max-defense/IpcSetting'),
        meta: { title: 'ipcSetting', keepAlive: false }
      },
      {
        path: 'ipcLinkage',
        name: 'maxIpcLinkage',
        component: () => import('@/views/max-defense/IpcLinkage'),
        meta: { title: 'ipcLinkage', keepAlive: false }
      }
    ]
  },
  // 用户反馈
  {
    path: '/user',
    component: layout,
    redirect: '/user/userFeedback',
    children: [
      {
        path: 'userFeedback',
        name: 'userFeedback',
        component: () => import(/* webpackChunkName: "userFeedback" */ '@/views/user-feedback/index'),
        meta: { title: 'userFeedback', keepAlive: false }
      },
      {
        path: 'feedbackPopup',
        name: 'feedbackPopup',
        component: () => import(/* webpackChunkName: "userFeedback" */ '@/views/user-feedback/FeedbackPopup'),
        meta: { title: 'userFeedback', keepAlive: false }
      }
    ]
  },
  // appBridge调试页面--调试H5与APP的协议调用
  {
    path: '/appBridge',
    component: layout,
    redirect: '/appBridge/index',
    children: [
      {
        path: 'index',
        name: 'appBridge',
        component: () => import('@/views/app-bridge/index'),
        meta: { title: 'appBridge', keepAlive: false }
      }
    ]
  },
  // 报警系统
  {
    path: '/alarmSystem',
    component: layout,
    redirect: '/alarmSystem/chooseSystem',
    children: [
      // 选择报警系统
      {
        path: 'chooseSystem',
        name: 'chooseSystem',
        component: () => import('@/views/alarm-system/ChooseSystem'),
        meta: { title: 'chooseSystem', keepAlive: false }
      },
      // 账户选项
      {
        path: 'chooseAccount',
        name: 'chooseAccount',
        component: () => import('@/views/alarm-system/ChooseAccount'),
        meta: { title: 'chooseAccount', keepAlive: false }
      },
      // 登录
      {
        path: 'alarmLogin',
        name: 'alarmLogin',
        component: () => import('@/views/alarm-system/AlarmLogin'),
        meta: { title: 'alarmLogin', keepAlive: false }
      },
      // 账户详情
      {
        path: 'accountDetails',
        name: 'accountDetails',
        component: () => import('@/views/alarm-system/AccountDetails'),
        meta: { title: 'accountDetails', keepAlive: false }
      },
      // 演示页面
      {
        path: 'demo',
        name: 'demo',
        component: () => import('@/views/alarm-system/Demo'),
        meta: { title: 'demo', keepAlive: false }
      }
    ]
  },
  {
    path: '/',
    component: layout,
    redirect: '/cloud/upgrade'
  },
  {
    path: '*', // 通配符
    component: notFoundCompnent, // 404页面
    meta: { hidden: true }
  }
]
