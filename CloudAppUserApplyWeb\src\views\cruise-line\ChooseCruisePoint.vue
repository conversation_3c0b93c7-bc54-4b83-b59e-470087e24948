<template>
  <div class="choose-point-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="choose-point-content">
      <div class="choose-point-list">
        <div
          class="choose-point-item"
          v-for="(item, index) in pointList"
          :key="'item.value' + index"
          @click="chooseItem(item)"
        >
          <div
            :class="['choose-point-name', 'text-over-ellipsis', pointRecord.index === item.index ? 'active-item' : '']"
          >
            {{ item.name }}
          </div>
          <img
            class="check-img"
            :class="uiStyleFlag === 'UI1B' ? 'vms-item-img' : ''"
            :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/checked.png')"
            v-if="pointRecord.index === item.index"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { mapState, mapMutations } from 'vuex'
export default {
  name: 'ChoosePoint',
  components: {
    NavBar
  },
  props: {},
  data() {
    return {}
  },
  mounted() {},
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    ...mapState('cruiseLine', ['pointRecord', 'pointList']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('cruiseLine', ['SET_POINT_RECORD']),
    back() {
      this.$router.go(-1)
    },
    chooseItem(item) {
      this.SET_POINT_RECORD({
        ...this.pointRecord,
        name: item.name,
        index: item.index
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-point-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .choose-point-content {
    width: 100%;
    height: calc(100% - 44px);
    padding: 35px 16px;
    box-sizing: border-box;
    overflow-y: auto;
  }
  .choose-point-list {
    width: 100%;
    border-radius: 10px;
    .choose-point-item {
      width: 100%;
      height: 60px;
      box-sizing: border-box;
      padding: 11px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: var(--font-size-body1-size, 16px);
      &:last-child {
        border: 0;
      }
      .check-img {
        width: 36px;
        height: 36px;
        position: relative;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.choose-point-wrapper .nav-bar .nav-bar-center {
  font-size: var(--font-size-body1-size, 16px) !important;
  font-weight: 600;
}
</style>
