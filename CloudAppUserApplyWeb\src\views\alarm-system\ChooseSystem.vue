<template>
  <div class="choose-system">
    <nav-bar title="Alarm System" @clickLeft="goBack" />
    <div class="choose-system-content">
      <div class="choose-system-choose">
        <div class="choose-title">Choose Alarm System</div>
        <van-radio-group v-model="selectedSystem">
          <div class="radio-item" v-for="(item, index) in alarmSystems" :key="index">
            <van-radio :name="item.value">{{ item.label }}</van-radio>
          </div>
        </van-radio-group>
      </div>

      <div class="choose-system-footer footer-btn">
        <van-button type="primary" block @click="handleNext">
          {{ $t('next') }}
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar.vue'
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'ChooseSystem',
  components: {
    NavBar
  },
  data() {
    return {
      selectedSystem: 'Tyco',
      alarmSystems: [
        { label: '<PERSON><PERSON>', value: '<PERSON><PERSON>' },
        { label: '<PERSON>co', value: 'Tyco' },
        { label: 'Risco', value: 'Risco' }
      ]
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('alarmSystem', ['SET_ALARM_SYSTEM_TYPE']),
    goBack() {
      this.$router.back()
    },
    handleNext() {
      // 保存选择的报警系统
      this.SET_ALARM_SYSTEM_TYPE(this.selectedSystem)

      // Tyco跳转到选择登录方式页面，其余跳转到登录页
      if (this.selectedSystem === 'Tyco') {
        this.$router.push({
          path: '/alarmSystem/chooseAccount',
          query: {
            systemType: this.selectedSystem
          }
        })
      } else {
        this.$router.push('/choose-account')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-system {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #333;

  &-content {
    flex: 1;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
  }

  &-switch {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;

    .switch-title {
      font-size: 16px;
      color: #fff;
    }
  }

  &-choose {
    margin-top: 20px;

    .choose-title {
      font-size: 16px;
      color: #fff;
      margin-bottom: 16px;
    }

    .radio-item {
      margin-bottom: 16px;

      .van-radio {
        display: flex;
        align-items: center;

        ::v-deep .van-radio__label {
          color: #fff;
          margin-left: 10px;
        }
      }
    }
  }

  &-footer {
    margin-top: auto;
    padding: 16px 0;

    .van-button {
      height: 44px;
      border-radius: 4px;
    }
  }
}
</style>
