<template>
  <div class="nav-bar">
    <div class="nav-bar-left">
      <slot name="left">
        <van-icon name="arrow-left" v-if="showBack" @click="$emit('clickLeft')" />
      </slot>
    </div>
    <div v-if="!searchFlag" class="nav-bar-center text-over-ellipsis2">{{ title || $t($route.meta.title) }}</div>
    <div v-else class="nav-bar-center">
      <van-search
        v-model="name"
        :show-action="!!name.trim()"
        :placeholder="placeholder"
        :action-text="$t('cancel')"
        @input="inputFun"
      />
    </div>
    <div class="nav-bar-right">
      <slot name="right">
        <div class="nav-bar-right-icon" v-if="showMore" @click="$emit('showMore')"><van-icon name="more-o" /></div>
        <div class="nav-bar-right-icon" v-if="showEllipsis" @click="$emit('showEllipsis')">
          <van-icon name="ellipsis" />
        </div>
        <div class="nav-bar-right-icon" v-if="showPlus" @click="$emit('showPlus')"><van-icon name="plus" /></div>
        <div class="nav-bar-right-icon" v-if="showSetting" @click="$emit('showSetting')">
          <van-icon class="van-icon iconfont" class-prefix="icon" name="vms-setting" />
        </div>
        <div class="nav-bar-right-icon" v-if="showShare" @click="$emit('showShare')">
          <theme-image alt="share" imageName="share.png" />
        </div>
      </slot>
    </div>
  </div>
</template>

<script>
import ThemeImage from '@/components/ThemeImage.vue'
export default {
  name: 'NavBar',
  components: {
    ThemeImage
  },
  props: {
    type: {
      type: Number, // 1-返回+标题   2-返回+搜索框
      default: 1
    },
    title: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    num: {
      default: ''
    },
    checked: {
      type: Boolean,
      default: false
    },
    searchName: {
      type: String,
      default: ''
    },
    showBack: {
      type: Boolean,
      default: true
    },
    showPlus: {
      type: Boolean,
      default: false
    },
    showMore: {
      type: Boolean,
      default: false
    },
    showEllipsis: {
      type: Boolean,
      default: false
    },
    showSetting: {
      type: Boolean,
      default: false
    },
    showShare: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      name: '',
      inputFun: this.$utils.debounceFun(this.handleInput),
      changFlag: true
    }
  },
  created() {
    this.name = this.searchName || ''
  },
  computed: {
    searchFlag() {
      return this.searchName || this.type === 2
    }
  },
  methods: {
    handleInput() {
      this.$emit('handleInput', this.name)
    }
  }
}
</script>

<style lang="scss" scoped>
.nav-bar {
  position: sticky;
  top: 0;
  z-index: 20;
  display: flex;
  align-items: center;
  width: 100%;
  height: 44px;
  line-height: 44px;
  .nav-bar-left {
    position: absolute;
    left: 0px;
    display: flex;
    align-items: center;
    padding-left: 12px;
    .van-icon {
      font-size: var(--font-size-h3-size, 24px);
    }
    .nav-bar-left-text {
      font-family: 'Source Han Sans CN', serif;
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      color: var(--text-color-primary, #1a1a1a);
    }
  }
  .nav-bar-right {
    position: absolute;
    right: 0px;
    display: flex;
    align-items: center;
    padding-right: 18px;
    .nav-bar-right-icon {
      margin-left: 8px;
      display: flex;
      align-items: center;
      .van-icon {
        font-size: var(--font-size-h3-size, 24px);
      }
      img {
        width: 24px;
        height: 24px;
      }
    }
  }
  .nav-bar-center {
    flex: 1;
    font-size: var(--font-size-h4-size, 20px);
    line-height: 20px;
    text-align: center;
    overflow: hidden;
    margin: 0px 30px;
    .van-search {
      width: 100%;
      padding: 5px 12px 5px 0px;
      ::v-deep.van-search__content {
        border-radius: 17px;
        background: #f4f8fb;
        .van-cell {
          padding: 0px 8px 0px 0px;
        }
        .van-icon-search {
          line-height: 34px;
        }
        .van-field__control {
          height: 34px;
        }
      }
    }
  }
}
</style>
