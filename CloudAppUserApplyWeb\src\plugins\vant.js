import {
  NavBar,
  Swipe,
  SwipeItem,
  Image as VanImage,
  Grid,
  GridItem,
  Cell,
  CellGroup,
  List,
  Icon,
  Tab,
  Tabs,
  Tabbar,
  TabbarItem,
  Toast,
  Search,
  Sidebar,
  SidebarItem,
  DropdownMenu,
  DropdownItem,
  Checkbox,
  CheckboxGroup,
  Popup,
  Form,
  Button,
  Field,
  PullRefresh,
  ShareSheet,
  IndexBar,
  IndexAnchor,
  Dialog,
  RadioGroup,
  Radio,
  Collapse,
  CollapseItem,
  Uploader,
  SwipeCell,
  Badge,
  Loading,
  Overlay,
  Skeleton,
  Switch,
  Picker,
  DatetimePicker,
  Progress
} from 'vant'

Toast.setDefaultOptions({
  position: 'bottom'
})

export default [
  NavBar,
  Swipe,
  SwipeItem,
  VanImage,
  Grid,
  GridItem,
  Cell,
  CellGroup,
  List,
  Icon,
  Tab,
  Tabs,
  Tabbar,
  TabbarItem,
  Toast,
  Search,
  Sidebar,
  SidebarItem,
  DropdownMenu,
  DropdownItem,
  Checkbox,
  CheckboxGroup,
  Popup,
  Form,
  Button,
  Field,
  PullRefresh,
  ShareSheet,
  IndexBar,
  IndexAnchor,
  Dialog,
  RadioGroup,
  Radio,
  Collapse,
  CollapseItem,
  Uploader,
  SwipeCell,
  Badge,
  Loading,
  Overlay,
  Skeleton,
  Switch,
  Picker,
  DatetimePicker,
  Progress
]
