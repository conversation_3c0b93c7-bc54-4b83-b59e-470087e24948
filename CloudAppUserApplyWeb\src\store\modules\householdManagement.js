export default {
  namespaced: true,
  state: () => ({
    roomNum: '',
    memberList: [],
    deviceList: [],
    buildingName: '',
    buildingNo: ''
  }),
  mutations: {
    INIT_ROOM(state) {
      state.roomNum = ''
      state.memberList = []
    },
    SET_ROOM_NUM(state, data) {
      state.roomNum = data
    },
    SET_MEMEBER_LIST(state, data) {
      state.memberList = data
    },
    DEL_MEMBER(state, data) {
      state.memberList.splice(data, 1)
    },
    ADD_MEMBER(state, data) {
      state.memberList.push(data)
    },
    UPDATE_MEMBER(state, data) {
      const { oldVal, newVal } = data

      const index = state.memberList.findIndex(member => member.memberName === oldVal.memberName)

      if (index > -1) {
        state.memberList.splice(index, 1, newVal)
      } else {
        throw new Error('can not find member to update')
      }
    },
    INIT_DEVICE_LIST(state) {
      state.deviceList = []
    },
    SET_DEVICE_LIST(state, data) {
      if (!data) {
        state.deviceList = []
      } else {
        state.deviceList = data.sort((a, b) => b.createTime - a.createTime)
      }
    },
    UPDATE_DEVICE_LIST(state, data) {
      const index = state.deviceList.findIndex(device => device.sn === data.sn)

      if (index > -1) {
        state.deviceList.splice(index, 1, data)
      } else {
        throw new Error('can not find device to update')
      }
    },
    INIT_BUILDING(state) {
      state.buildingName = ''
      state.buildingNo = ''
    },
    SET_BUILDING_NAME(state, data) {
      state.buildingName = data
    },
    SET_BUILDING_NO(state, data) {
      state.buildingNo = data
    }
  }
}
