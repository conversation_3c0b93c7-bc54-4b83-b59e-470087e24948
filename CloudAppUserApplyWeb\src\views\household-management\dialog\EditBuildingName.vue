<template>
  <div class="household-manangement-dialog">
    <van-dialog
      v-model="show"
      :title="$t('buildingName')"
      show-cancel-button
      :before-close="onBeforeClose"
      @confirm="handleConfirm"
      @cancel="cancel"
      @open="clearParam"
      :cancelButtonText="$t('cancel')"
      :confirmButtonText="$t('confirm')"
    >
      <div class="content-div">
        <div class="cruise-line-div">
          <input
            type="text"
            class="common-input"
            v-model="buildingName"
            maxlength="24"
            @input="replaceInput"
            :placeholder="$t('pleaseEnter')"
          />
          <span class="cruise-line-close">
            <img
              :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/input_close.png')"
              v-if="buildingName"
              @click="buildingName = ''"
            />
          </span>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'editBuildingName',
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  components: {},
  data() {
    return {
      show: false,
      buildingName: ''
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val !== this.buildingName) {
          this.buildingName = val
        }
      },
      immediate: true
    }
  },
  mounted() {},
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    handleConfirm() {
      if (!this.buildingName || !this.buildingName.trim()) {
        this.$toast(this.$t('pleaseEnter'))
        return false
      }

      const name = this.buildingName.trim()

      if (name === this.value.trim()) {
        // 说明没有改动，不用调接口
        this.cancel()
        return
      }

      if (!/^[a-zA-Z0-9\-_\u4e00-\u9fa5 ]*$/.test(name)) {
        this.$toast({
          message: this.$t('supportDash'),
          position: 'top'
        })
        return false
      }

      this.$emit('confirm', this.buildingName.trim())
    },
    replaceInput() {
      const invalidReg = /[^a-zA-Z0-9\-_\u4e00-\u9fa5 ]+/g
      const name = this.buildingName

      if (invalidReg.test(name)) {
        this.buildingName = name.replaceAll(invalidReg, '')
        this.$toast({
          message: this.$t('supportDash'),
          position: 'top'
        })
      }
    },
    // confirm 确认按钮；before-close控制关闭前的回调
    onBeforeClose(action, done) {
      if (action === 'confirm') {
        return done(false)
      } else {
        return done(true)
      }
    },
    cancel() {
      this.show = false
      this.$emit('cancel')
    },
    // 清除数据
    clearParam() {
      this.buildingName = this.value
    }
  }
}
</script>
<style lang="scss" scoped>
.household-manangement-wrapper {
  ::v-deep.van-dialog__header {
    font-weight: 700;
    font-size: var(--font-size-body1-size, 16px);
  }
  .content-div {
    width: 264px;
    margin: auto;
  }
  .cruise-line-div {
    width: 260px;
    height: 32px;
    line-height: 32px;
    margin-bottom: 12px;
    padding: 4px 2px;
    border-radius: 6px;
    margin-top: 20px;
    position: relative;

    .cruise-line-close {
      position: absolute;
      right: 10px;
      top: 8px;
      img {
        width: 24px;
        height: 24px;
      }
    }
  }
  .common-input {
    width: 220px;
    padding-left: 8px;
  }
}
</style>
