.household-manangement-wrapper,
.household-manangement-dialog {
  .input-item {
    border-bottom: 1px solid $UI1E-light-gray-color;

    &::after {
      border: none;
    }
  }

  .error-msg {
    color: $UI1E-red-color;
  }

  .footer-btn {
    color: $UI1E-white-color;
  }

  .room-member-input {
    background-color: transparent;

    .van-field__control {
      color: $UI1E-white-color;
    }
  }

  .van-dialog__header {
    color: $UI1E-font-color;
  }

  .cruise-line-div {
    border: 1px solid $UI1E-light-gray-color;
    background-color: $UI1E-background-color;

    .common-input {
      background-color: transparent;
    }
  }

  .tvt-better-scroll {
    .household-item {
      background: $UI1E-light-background-color;
      color: $UI1E-font-color;

      .household-title {
        color: $UI1E-white-color;

      }

      .household-line-text {
        color: $vms-gray;
      }

      .required-icon {
        color: $vms-red;
      }

      .right-value {
        color: $vms-gray;
      }

      &:not(:last-child) {
        border-bottom: 1px solid $UI1E-light-gray-color;
      }

      &::after {
        border-color: $UI1E-light-gray-color;
      }
    }

    .room-header {
      color: $vms-gray;
    }

    .device-select-icon {
      &.success {
        color: $UI1E-font-color;
      }
    }
  }

  .no-data {
    .add-btn {
      background-color: $UI1E-color-primary;
      color: $UI1E-white-color;
    }
  }

  .no-data-text {
    .footer-btn {
      background-color: $UI1E-color-primary;
      color: $UI1E-white-color;
    }
  }
}