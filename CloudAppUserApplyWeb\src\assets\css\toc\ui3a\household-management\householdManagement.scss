.household-manangement-wrapper,
.household-manangement-dialog {
  .input-item {
    border-bottom: 1px solid $UI3A-light-gray-color;

    &::after {
      border: none;
    }
  }

  .error-msg {
    color: $UI3A-red-color;
  }

  .footer-btn {
    color: $UI3A-white-color;
  }

  .room-member-input {
    background-color: transparent;

    .van-field__control {
      color: $UI3A-white-color;
    }
  }

  .van-dialog__header {
    color: $UI3A-font-color;
  }

  .cruise-line-div {
    border: 1px solid $UI3A-light-gray-color;
    background-color: $UI3A-background-color;

    .common-input {
      background-color: transparent;
    }
  }

  .tvt-better-scroll {
    .household-item {
      background: $UI3A-light-background-color;
      color: $UI3A-font-color;

      .household-title {
        color: $UI3A-white-color;

      }

      .household-line-text {
        color: $vms-gray;
      }

      .required-icon {
        color: $vms-red;
      }

      .right-value {
        color: $vms-gray;
      }

      &:not(:last-child) {
        border-bottom: 1px solid $UI3A-light-gray-color;
      }

      &::after {
        border-color: $UI3A-light-gray-color;
      }
    }

    .room-header {
      color: $vms-gray;
    }

    .device-select-icon {
      &.success {
        color: $UI3A-font-color;
      }
    }
  }

  .no-data {
    .add-btn {
      background-color: $UI3A-color-primary;
      color: $UI3A-white-color;
    }
  }

  .no-data-text {
    .footer-btn {
      background-color: $UI3A-color-primary;
      color: $UI3A-white-color;
    }
  }
}