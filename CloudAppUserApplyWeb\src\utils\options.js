import i18n from '@/lang'

export const STATUS_TYPE = () => ({
  1: {
    text: i18n.t('online'),
    status: 'success'
  },
  2: {
    text: i18n.t('offline'),
    status: 'error'
  }
})

// 设备升级，APP支持显示升级状态：下载中、下载失败、升级中、升级失败 升级成功、离线；根据设备返回状态显示
export const STATUS_COLOR = () => ({
  latest: '#00bd28',
  waitingForUpgrade: '#00BAFF',
  downloading: '#00BAFF',
  downloadFail: '#FF3A39',
  downloadNetException: '#FF3A39',
  downloadFailNodeInvalid: '#FF3A39',
  downloadFailOSSException: '#FF3A39',
  downloadFailNodeDisconnect: '#FF3A39',
  downloadFailFileWritExecption: '#FF3A39',
  downloadFailFileReadExecption: '#FF3A39',
  downloadFailFileOpenExecption: '#FF3A39',
  downloadSuccess: '#00BAFF',
  installing: '#00BAFF',
  installSuccess: '#00bd28',
  installFail: '#FF3A39',
  installFailNodeDisconnect: '#FF3A39',
  installFailNodeInvalid: '#FF3A39',
  offLine: '#FF3A39',
  // IPC升级过程中的状态
  normal: '#00BAFF',
  upgradePrepare: '#00BAFF',
  upgrading: '#00BAFF',
  upgradeFail: '#FF3A39'
})

// 设备升级，APP支持显示升级状态：下载中、下载失败、升级中、升级失败 升级成功、离线；根据设备返回状态显示
export const STATUS_COLOR_UI1B = () => ({
  latest: '#181818',
  waitingForUpgrade: '#181818',
  downloading: '#181818',
  downloadFail: '#FF3D3D',
  downloadNetException: '#FF3D3D',
  downloadFailNodeInvalid: '#FF3D3D',
  downloadFailOSSException: '#FF3D3D',
  downloadFailNodeDisconnect: '#FF3D3D',
  downloadFailFileWritExecption: '#FF3D3D',
  downloadFailFileReadExecption: '#FF3D3D',
  downloadFailFileOpenExecption: '#FF3D3D',
  downloadSuccess: '#181818',
  installing: '#181818',
  installSuccess: '#181818',
  installFail: '#FF3D3D',
  installFailNodeDisconnect: '#FF3D3D',
  installFailNodeInvalid: '#FF3D3D',
  offLine: '#FF3D3D'
})

// 设备升级，APP支持显示升级状态：下载中、下载失败、升级中、升级失败 升级成功、离线；根据设备返回状态显示
export const STATUS_COLOR_UI1C = () => ({
  latest: '#000000',
  waitingForUpgrade: '#000000',
  downloading: '#000000',
  downloadFail: '#FF4D4F',
  downloadNetException: '#FF4D4F',
  downloadFailNodeInvalid: '#FF4D4F',
  downloadFailOSSException: '#FF4D4F',
  downloadFailNodeDisconnect: '#FF4D4F',
  downloadFailFileWritExecption: '#FF4D4F',
  downloadFailFileReadExecption: '#FF4D4F',
  downloadFailFileOpenExecption: '#FF4D4F',
  downloadSuccess: '#000000',
  installing: '#000000',
  installSuccess: '#000000',
  installFail: '#FF4D4F',
  installFailNodeDisconnect: '#FF4D4F',
  installFailNodeInvalid: '#FF4D4F',
  offLine: '#FF4D4F'
})

// 托管状态  0待接收/1已接收/2拒绝/3删除
export const TRUSTEESHIP_STATUS = () => [
  { label: i18n.t('waitReceived'), value: 0 },
  { label: i18n.t('received'), value: 1 },
  { label: i18n.t('refuse'), value: 2 },
  { label: i18n.t('delete'), value: 3 }
]

// 托管 权限配置选项
export const AUTH_LIST = () => [
  { label: i18n.t('live'), value: 'live' },
  { label: i18n.t('rec'), value: 'rec' },
  { label: i18n.t('config'), value: 'config' }
]

// 托管有效期选项 30分钟、2小时、10小时、24小时、1周、永久 0表示永久  时间value换算成秒
export const TRUSTEESHIP_VALIDITY_LIST = () => [
  { label: i18n.t('manyMinutes', [30]), value: 30 * 60 },
  { label: i18n.t('manyHours', [2]), value: 2 * 60 * 60 },
  { label: i18n.t('manyHours', [10]), value: 10 * 60 * 60 },
  { label: i18n.t('manyHours', [24]), value: 24 * 60 * 60 },
  { label: i18n.t('oneWeek'), value: 1 * 7 * 24 * 60 * 60 },
  { label: i18n.t('forever'), value: 0 }
]

export const TRUSTEESHIP_VALIDITY_LIST_FULLNAME = () => [
  { label: i18n.t('manyMinutesEn', [30]), value: 30 * 60 },
  { label: i18n.t('manyHoursEn', [2]), value: 2 * 60 * 60 },
  { label: i18n.t('manyHoursEn', [10]), value: 10 * 60 * 60 },
  { label: i18n.t('manyHoursEn', [24]), value: 24 * 60 * 60 },
  { label: i18n.t('oneWeek'), value: 1 * 7 * 24 * 60 * 60 },
  { label: i18n.t('forever'), value: 0 }
]

// 托管有效期选项 1小时、2小时、4小时、8小时、永久 0表示永久  时间value换算成秒
export const HOSTING_VALIDITY_LIST = () => [
  { label: i18n.t('manyHours', [1]), value: 1 * 60 * 60 },
  { label: i18n.t('manyHours', [2]), value: 2 * 60 * 60 },
  { label: i18n.t('manyHours', [4]), value: 4 * 60 * 60 },
  { label: i18n.t('manyHours', [8]), value: 8 * 60 * 60 },
  { label: i18n.t('forever'), value: 0 }
]

// 云升级状态文字颜色-- 分为四类 “下载中”为主题色  待升级：#ff8000  升级失败：#ff3535  升级成功：#00bd28
const Theme_Color_Obj = {
  tob: {
    ui1b: '#2C5BFA'
  },
  toc: {
    ui1a: '#00BAFF',
    ui1b: '#00BC70',
    ui1c: '#D90000',
    ui1d: '#9E0000',
    ui1e: '#C5D700',
    ui1f: '#1379AA',
    ui1g: '#FFCB33',
    ui1h: '#17B9B0',
    ui1i: '#3773ff',
    ui1j: '#3773ff',
    ui2a: '#00BAFF',
    ui2b: '#00C261'
  },
  top: {
    ui1c: '#2C5BFA' // 中性
  }
}

// 待下载状态文字颜色
const Wait_Theme_Color_Obj = {
  tob: {},
  toc: {
    ui2b: '#00C261'
  },
  top: {}
}

// 升级失败状态文字颜色
const Fail_Theme_Color_Obj = {
  tob: {},
  toc: {
    ui2b: '#FF3D3D'
  },
  top: {}
}

// 对接云后台的云升级状态颜色
export const STATUS_COLOR_THEME = (appType, style) => ({
  latest: '#00bd28', // 最新版用绿色
  waitingForUpgrade: Wait_Theme_Color_Obj[appType][style] || '#ff8000',
  downloading: Theme_Color_Obj[appType][style] || '#ff3535', // 下载中用主题色
  downloadFail: Fail_Theme_Color_Obj[appType][style] || '#ff3535',
  downloadNetException: '#ff3535',
  downloadFailNodeInvalid: '#ff3535',
  downloadFailOSSException: '#ff3535',
  downloadFailNodeDisconnect: '#ff3535',
  downloadFailFileWritExecption: '#ff3535',
  downloadFailFileReadExecption: '#ff3535',
  downloadFailFileOpenExecption: '#ff3535',
  downloadSuccess: Theme_Color_Obj[appType][style] || '#00bd28',
  installing: Theme_Color_Obj[appType][style] || '#ff3535', // 升级中用主题色
  installSuccess: '#00bd28',
  installFail: Fail_Theme_Color_Obj[appType][style] || '#ff3535',
  installFailNodeDisconnect: Fail_Theme_Color_Obj[appType][style] || '#ff3535',
  installFailNodeInvalid: Fail_Theme_Color_Obj[appType][style] || '#ff3535',
  offLine: Fail_Theme_Color_Obj[appType][style] || '#ff3535',
  // IPC升级过程中的状态
  normal: Theme_Color_Obj[appType][style] || '#00BAFF',
  upgradePrepare: Theme_Color_Obj[appType][style] || '#00BAFF',
  upgrading: Theme_Color_Obj[appType][style] || '#00BAFF',
  upgradeFail: Fail_Theme_Color_Obj[appType][style] || '#FF3A39',
  // 云后台云升级状态
  repeatedlyRestart: Fail_Theme_Color_Obj[appType][style] || '#ff3535',
  versionUnchanged: Fail_Theme_Color_Obj[appType][style] || '#ff3535',
  versionException: Fail_Theme_Color_Obj[appType][style] || '#ff3535'
})

// 布防/撤防状态文字颜色-- 分为三类 失败: #ff3535  成功：#00bd28 “下载中”为主题色
export const DEFENSE_STATUS_COLOR = (appType, style) => ({
  0: Theme_Color_Obj[appType][style], // 下载中中主题色
  1: '#00bd28', // 成功
  2: '#ff3535' // 失败
})

// IPC联动项
export const IPC_LINKAGE_LIST_FULLNAME = () => [
  { label: i18n.t('ipcSound'), value: 'ao' }, // 能力集对应 ao  是否支持音频告警输出
  { label: i18n.t('ipcLight'), value: 'wl' } // 能力集对应 wl 是否白光灯告警控制
]

// 撤防联动项跟设备字段的对应关系
export const LINKAGE_DEVIEC_OBJ = {
  ao: 'nodeAudioSwitch',
  wl: 'nodeLightSwitch'
}

export const DEVICE_CAPABILITY_MAP = {
  TALK: 'talk',
  CONFIG: 'config',
  ARM_BY_CLOUD: 'arm'
}

// 分享时通道能力集 -- 预览、回放、报警、对讲、云台（对讲和云台根据通道能力集展示），布撤防
export const CHANNEL_CAPABILITY_LIST = () => [
  { label: i18n.t('preview'), value: 'live.video,live.audio', disabled: true }, // 预览  disabled表示不可编辑
  { label: i18n.t('playback'), value: 'rec.video,rec.audio' }, // 回放
  { label: i18n.t('alarmNotification'), value: 'alert' }, // 报警
  { label: i18n.t('intercom'), value: 'talk', supportAuth: 'tb', filterAble: true }, // supportAuth 能力集对应tb 通道是否支持对讲 filterAble表示可以被过滤
  { label: i18n.t('gimbal'), value: 'ptz', supportAuth: 'ptz', filterAble: true } // supportAuth 能力集对应ptz 是否支持云台控制
]

// 设备托管时设备能力
export const DEVICE_CAPABILITY_LIST = () => [
  { label: i18n.t('config'), value: 'config', disabled: true }, // 设置 disabled表示不可编辑
  { label: i18n.t('live'), value: 'live' }, // 预览
  { label: i18n.t('rec'), value: 'rec' } // 回放
]

// super live max IPC联动项
export const MAX_IPC_LINKAGE_LIST_FULLNAME = () => [
  { label: i18n.t('ipcSound'), value: 'ao', filterAble: true }, // 能力集对应 ao  是否支持音频告警输出  filterAble表示可以被过滤
  { label: i18n.t('ipcLight'), value: 'wl', filterAble: true }, // 能力集对应 wl 是否白光灯告警控制  filterAble表示可以被过滤
  { label: i18n.t('msgPushSwitch'), value: 'sub' }, // 能力集对应 sub 安全访问能力,支持订阅2.0协议 -- 推送
  { label: i18n.t('alarmOut'), value: 'mao' }, // 能力集对应 mao 是否支持手动控制告警输出 -- 报警输出
  { label: i18n.t('presetPoint'), value: 'baseptz' } // 能力集对应 baseptz 支持PTZ操作、预置点、巡航线 -- 预置点
]

// super live max 撤防联动项跟设备字段的对应关系
export const MAX_LINKAGE_DEVIEC_OBJ = {
  ao: 'nodeAudioSwitch',
  wl: 'nodeLightSwitch',
  sub: 'msgPushSwitch',
  mao: 'alarmOut',
  baseptz: 'preset'
}
