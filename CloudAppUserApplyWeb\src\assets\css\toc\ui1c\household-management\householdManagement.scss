.household-manangement-wrapper,
.household-manangement-dialog {
  .input-item {
    border-bottom: 1px solid $UI1C-light-gray-color;

    &::after {
      border: none;
    }
  }

  .error-msg {
    color: $UI1C-red-color;
  }

  .footer-btn {
    color: $UI1C-white-color;
  }

  .room-member-input {
    background-color: transparent;

    .van-field__control {
      color: $UI1C-white-color;
    }
  }

  .van-dialog__header {
    color: $UI1C-font-color;
  }

  .cruise-line-div {
    border: 1px solid $UI1C-light-gray-color;
    background-color: $UI1C-background-color;

    .common-input {
      background-color: transparent;
    }
  }

  .tvt-better-scroll {
    .household-item {
      background: $UI1C-light-background-color;
      color: $UI1C-font-color;

      .household-title {
        color: $UI1C-white-color;

      }

      .household-line-text {
        color: $vms-gray;
      }

      .required-icon {
        color: $vms-red;
      }

      .right-value {
        color: $vms-gray;
      }

      &:not(:last-child) {
        border-bottom: 1px solid $UI1C-light-gray-color;
      }

      &::after {
        border-color: $UI1C-light-gray-color;
      }
    }

    .room-header {
      color: $vms-gray;
    }

    .device-select-icon {
      &.success {
        color: $UI1C-font-color;
      }
    }
  }

  .no-data {
    .add-btn {
      background-color: $UI1C-color-primary;
      color: $UI1C-white-color;
    }
  }

  .no-data-text {
    .footer-btn {
      background-color: $UI1C-color-primary;
      color: $UI1C-white-color;
    }
  }
}