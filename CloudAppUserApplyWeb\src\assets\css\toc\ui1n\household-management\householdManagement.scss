.household-manangement-wrapper,
.household-manangement-dialog {
  .input-item {
    border-bottom: 1px solid $UI1N-light-gray-color;

    &::after {
      border: none;
    }
  }

  .error-msg {
    color: $UI1N-red-color;
  }

  .footer-btn {
    color: $UI1N-white-color;
  }

  .room-member-input {
    background-color: transparent;

    .van-field__control {
      color: $UI1N-white-color;
    }
  }

  .van-dialog__header {
    color: $UI1N-font-color;
  }

  .cruise-line-div {
    border: 1px solid $UI1N-light-gray-color;
    background-color: $UI1N-background-color;

    .common-input {
      background-color: transparent;
    }
  }

  .tvt-better-scroll {
    .household-item {
      background: $UI1N-light-background-color;
      color: $UI1N-font-color;

      .household-title {
        color: $UI1N-white-color;

      }

      .household-line-text {
        color: $vms-gray;
      }

      .required-icon {
        color: $vms-red;
      }

      .right-value {
        color: $vms-gray;
      }

      &:not(:last-child) {
        border-bottom: 1px solid $UI1N-light-gray-color;
      }

      &::after {
        border-color: $UI1N-light-gray-color;
      }
    }

    .room-header {
      color: $vms-gray;
    }

    .device-select-icon {
      &.success {
        color: $UI1N-font-color;
      }
    }
  }

  .no-data {
    .add-btn {
      background-color: $UI1N-color-primary;
      color: $UI1N-white-color;
    }
  }

  .no-data-text {
    .footer-btn {
      background-color: $UI1N-color-primary;
      color: $UI1N-white-color;
    }
  }
}