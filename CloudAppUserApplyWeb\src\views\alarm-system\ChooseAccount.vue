<template>
  <div class="choose-account">
    <nav-bar title="Alarm System" @clickLeft="goBack" />
    <div class="choose-account-content">
      <div class="account-title">Account</div>

      <div class="choose-account-choose">
        <van-radio-group v-model="selectedAccountType">
          <div class="radio-item" v-for="(item, index) in accountTypes" :key="index">
            <van-radio :name="item.value">{{ item.label }}</van-radio>
          </div>
        </van-radio-group>
      </div>

      <div class="choose-account-footer footer-btn">
        <van-button type="primary" block @click="handleNext" :disabled="!selectedAccountType"> Next </van-button>
      </div>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar.vue'
import { mapState } from 'vuex'

export default {
  name: 'ChooseAccount',
  components: {
    NavBar
  },
  data() {
    return {
      selectedAccountType: 'existing',
      accountTypes: [
        { label: 'I already have an account', value: 'existing' },
        { label: 'Use my Provision Cam2 account', value: 'provision' },
        { label: 'I want to open my own account', value: 'new' }
      ]
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    handleNext() {
      // 根据不同的账号类型进行不同的跳转
      const systemType = this.$route.query.systemType
      switch (this.selectedAccountType) {
        case 'existing':
          this.$router.push({
            path: '/alarmSystem/alarmLogin',
            query: {
              systemType
            }
          })
          break
        case 'provision':
          this.$router.push('/alarmSystem/provision-account')
          break
        case 'new':
          this.$router.push('/alarmSystem/register')
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-account {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #333;

  &-content {
    flex: 1;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
  }

  .account-title {
    font-size: 18px;
    color: #fff;
    margin-bottom: 20px;
    text-align: center;
  }

  &-choose {
    margin-top: 10px;

    .radio-item {
      margin-bottom: 20px;

      .van-radio {
        display: flex;
        align-items: center;

        ::v-deep .van-radio__label {
          color: #fff;
          margin-left: 10px;
          font-size: 16px;
        }
      }
    }
  }

  &-footer {
    margin-top: auto;
    padding: 16px 0;

    .van-button {
      height: 44px;
      border-radius: 4px;
    }
  }
}
</style>
