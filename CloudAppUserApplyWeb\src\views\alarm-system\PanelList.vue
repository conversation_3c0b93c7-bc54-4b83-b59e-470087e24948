<template>
  <div class="panel-list">
    <nav-bar title="Alarm System" @clickLeft="goBack" />
    <div class="panel-list-content">
      <div class="panel-title">Panel List</div>
      
      <div class="panel-items" v-if="panelList.length > 0">
        <div 
          v-for="(panel, index) in panelList" 
          :key="index"
          class="panel-item"
          @click="selectPanel(panel)"
        >
          <div class="panel-info">
            <div class="panel-name">{{ panel.name }}</div>
            <div class="panel-status" :class="panel.status">
              {{ panel.status === 'online' ? 'Online' : 'Offline' }}
            </div>
          </div>
          <div class="panel-arrow">
            <van-icon name="arrow" />
          </div>
        </div>
      </div>
      
      <div class="empty-state" v-else>
        <div class="empty-icon">
          <van-icon name="warning-o" />
        </div>
        <div class="empty-text">No panels found</div>
        <div class="empty-description">
          Please check your connection or contact support
        </div>
      </div>
      
      <div class="panel-actions">
        <van-button type="primary" block @click="refreshPanels">
          Refresh
        </van-button>
        
        <van-button type="default" block @click="addPanel" class="mt-16">
          Add Panel
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar.vue'
import { mapState } from 'vuex'

export default {
  name: 'PanelList',
  components: {
    NavBar
  },
  data() {
    return {
      panelList: [
        {
          id: 1,
          name: 'Main Panel',
          status: 'online',
          location: 'Building A - Floor 1'
        },
        {
          id: 2,
          name: 'Secondary Panel',
          status: 'offline',
          location: 'Building B - Floor 2'
        },
        {
          id: 3,
          name: 'Emergency Panel',
          status: 'online',
          location: 'Building A - Basement'
        }
      ]
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('alarmSystem', ['alarmSystemType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  created() {
    this.loadPanels()
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    loadPanels() {
      // 这里可以调用API加载面板列表
      // 目前使用模拟数据
      this.$toast.loading({
        message: 'Loading panels...',
        forbidClick: true,
        duration: 1000
      })
      
      setTimeout(() => {
        this.$toast.clear()
      }, 1000)
    },
    selectPanel(panel) {
      this.$toast(`Selected: ${panel.name}`)
      // 这里可以跳转到面板详情页面
      // this.$router.push(`/alarmSystem/panelDetail/${panel.id}`)
    },
    refreshPanels() {
      this.$toast.loading({
        message: 'Refreshing...',
        forbidClick: true,
        duration: 1500
      })
      
      setTimeout(() => {
        this.$toast.clear()
        this.$toast.success('Panels refreshed')
      }, 1500)
    },
    addPanel() {
      this.$toast('Add panel functionality coming soon')
      // 这里可以跳转到添加面板页面
      // this.$router.push('/alarmSystem/addPanel')
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-list {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #333;

  &-content {
    flex: 1;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
  }

  .panel-title {
    font-size: 18px;
    color: #fff;
    margin-bottom: 20px;
    text-align: center;
  }

  .panel-items {
    flex: 1;
    margin-bottom: 20px;
  }

  .panel-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #444;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #555;
    }

    &:active {
      background-color: #555;
    }

    .panel-info {
      flex: 1;

      .panel-name {
        font-size: 16px;
        color: #fff;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .panel-status {
        font-size: 14px;
        
        &.online {
          color: #52c41a;
        }
        
        &.offline {
          color: #ff4d4f;
        }
      }
    }

    .panel-arrow {
      color: #999;
      
      .van-icon {
        font-size: 16px;
      }
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #999;

    .empty-icon {
      margin-bottom: 16px;
      
      .van-icon {
        font-size: 48px;
      }
    }

    .empty-text {
      font-size: 18px;
      margin-bottom: 8px;
    }

    .empty-description {
      font-size: 14px;
      line-height: 1.4;
    }
  }

  .panel-actions {
    .van-button {
      height: 44px;
      border-radius: 4px;
      font-size: 16px;
    }
    
    .mt-16 {
      margin-top: 16px;
    }
  }
}
</style>
