<template>
  <div class="household-manangement-wrapper">
    <nav-bar :title="isAdd ? $t('addRoomMember') : $t('householdManagement')" @clickLeft="back"></nav-bar>
    <van-tabs v-model="active">
      <van-tab :title="$t('email')" name="email">
        <van-field
          class="input-item room-member-input"
          v-model.trim="email"
          :placeholder="$t('enterMemberEmail')"
          maxlength="50"
          @blur="e => validateEmail()"
        >
          <template #right-icon>
            <span class="cruise-line-close">
              <img
                :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/input_close.png')"
                v-if="email"
                @click="clearEmail"
              />
            </span>
          </template>
        </van-field>
        <span class="error-msg" v-show="emailErrorMsg"> {{ emailErrorMsg }} </span>
      </van-tab>
      <van-tab :title="$t('mobile')" name="mobile">
        <mobile-input ref="mobileRef" v-model="mobile" />
      </van-tab>
    </van-tabs>
    <div class="footer">
      <van-button class="footer-btn" type="primary" :loading="addLoading" @click="handleClick">
        {{ $t('confirm') }}
      </van-button>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { addRoomMembers } from '@/api/householdManagement.js'
import { validateEmail } from '@/utils/validate'
import MobileInput from './components/MobileInput.vue'
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'AddRoomMember',
  components: {
    NavBar,
    MobileInput
  },
  props: {},
  data() {
    return {
      isRoomAdd: false, // 上层的房间分为编辑和新增
      isAdd: false, // 编辑或者新增住户
      active: 'email',
      email: '',
      mobile: '',
      emailErrorMsg: '',
      roomdId: '',
      addLoading: false
    }
  },
  mounted() {
    // 编辑房间
    if (this.$route.query.id) {
      this.isRoomAdd = false
      this.roomdId = this.$route.query.id
    } else {
      // 添加房间
      this.isRoomAdd = true
    }

    const { name } = this.$route.query
    if (name) {
      if (name.indexOf('@') > -1) {
        this.active = 'email'
        this.email = name
      } else {
        this.active = 'mobile'
        this.mobile = name
      }
      this.isAdd = false
    } else {
      this.isAdd = true
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('householdManagement', ['memberList']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('householdManagement', ['ADD_MEMBER', 'UPDATE_MEMBER']),
    back() {
      this.$router.go(-1)
    },
    async handleClick() {
      if (this.isRoomAdd && this.validateFields()) {
        if (this.isAdd) {
          this.ADD_MEMBER({
            roomId: this.roomdId,
            memberName: this.getCurrentValue()
          })
        } else {
          this.UPDATE_MEMBER({
            oldVal: {
              roomId: this.roomdId,
              memberName: this.$route.query.name
            },
            newVal: {
              roomId: this.roomdId,
              memberName: this.getCurrentValue()
            }
          })
        }

        this.back()
      } else {
        // 编辑房间

        try {
          this.$loading.show()

          if (this.validateFields()) {
            if (this.isAdd) {
              this.addLoading = true
              // 立即新增
              await addRoomMembers({
                roomId: this.roomdId,
                memberNames: [this.getCurrentValue()]
              })
              this.$toast(this.$t('addSuccess'))
            } else {
              // 立即保存
              // 编辑房间
              // 暂时不能编辑住户, 不会走到这个逻辑
              console.error('can not edit room member')
            }

            this.back()
          }
        } catch (error) {
          console.error(error)
        } finally {
          this.$loading.hide()
          this.addLoading = false
        }
      }
    },
    getCurrentValue() {
      if (this.active === 'email') {
        return this.email
      }

      return this.mobile
    },
    validateFields() {
      if (this.active === 'email') {
        return this.validateEmail(true)
      }

      return this.$refs.mobileRef.validateMobile(true, this.$route.query.name)
    },
    validateEmail(validateEmpty = false) {
      if (this.email === '') {
        if (validateEmpty) {
          this.emailErrorMsg = this.$t('emailNameNotEmpty')
          return false
        }

        this.emailErrorMsg = ''
        return true
      }

      if (!validateEmail(this.email)) {
        this.emailErrorMsg = this.$t('emailNameError')
        return false
      }

      const hasSame = this.memberList.find(member => member.memberName.toLowerCase() === this.email.toLowerCase())

      if (this.isAdd) {
        if (hasSame) {
          this.emailErrorMsg = this.$t('emailRepeate')
          return false
        }
      } else {
        if (hasSame && this.email !== this.$route.query.name) {
          this.emailErrorMsg = this.$t('emailRepeate')
          return false
        }
      }

      this.emailErrorMsg = ''
      return true
    },

    clearEmail() {
      this.email = ''
      this.emailErrorMsg = ''
    }
  }
}
</script>
<style lang="scss" scoped>
.household-manangement-wrapper {
  height: 100%;
  overflow: auto;

  .footer {
    position: fixed;
    bottom: 20px;
    width: 100%;
    // padding: 20px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .footer-btn {
      width: 296px;
      border-radius: 23px;
      line-height: 46px;
      text-align: center;
    }
  }

  .input-item {
    width: 80%;
    padding: 5px 0;
    font-size: var(--font-size-h5-size, 18px);
    margin: auto;

    .cruise-line-close {
      display: flex;
      align-items: center;
      img {
        width: 24px;
        height: 24px;
      }
    }
  }

  .error-msg {
    display: block;
    width: 80%;
    padding-top: 4px;
    margin: auto;
  }

  &::v-deep .van-tabs--line {
    height: calc(100% - 44px);

    .van-tabs__content {
      margin-top: 10px;
      height: calc(100% - 54px);

      .van-tab__pane {
        padding-top: 40%;
      }
    }
  }
}
</style>
