<template>
  <div class="household-manangement-wrapper">
    <nav-bar :title="isAdd ? $t('addRoom') : $t('householdManagement')" @clickLeft="back"></nav-bar>
    <tvt-better-scroll class="tvt-better-scroll" @pullingDown="pullingDown" :pullingStatus="pullingStatus">
      <van-cell class="household-item" name="name" is-link @click="editRoomNum">
        <template #title> <span class="required-icon">*</span>{{ $t('roomName') }} </template>
        <template #default>
          <span class="right-value text-over-ellipsis">{{ roomNum }}</span>
        </template>
      </van-cell>

      <div class="room-container">
        <div class="room-header">
          <span class="room-title">
            {{ $t('household') }}
          </span>
          <span class="room-add" @click="addHouseHold">
            <van-icon name="plus" />
          </span>
        </div>
        <div class="room-list" v-if="loading || memberList.length">
          <van-cell
            v-for="(item, index) in memberList"
            :key="item.buildingMemberId"
            class="room-item"
            :name="item.buildingMemberId"
            :is-link="isAdd"
            @click="editHouseHold(item)"
          >
            <template #title>
              <span class="room-name text-over-ellipsis">{{ formatPhoneNumber(item.memberName) }}</span>
            </template>
            <template #right-icon>
              <theme-image
                @click="deleteHouseHold(item, index)"
                class="refuse-img van-cell-right-icon"
                alt="delete"
                imageName="delete_danger.png"
              />
            </template>
          </van-cell>
          <!-- <van-swipe-cell v-for="(item, index) in memberList" :key="item.buildingMemberId">
            <van-cell class="room-item" :name="item.buildingMemberId" :is-link="isAdd" @click="editHouseHold(item)">
              <template #title>
                <span class="room-name text-over-ellipsis">{{ formatPhoneNumber(item.memberName) }}</span>
              </template>
            </van-cell>
            <template #right>
              <van-button square type="danger" class="swipe-right-btn" @click="deleteHouseHold(item, index)">
                <img
                  class="refuse-img"
                  :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/refuse.png')"
                />
              </van-button>
            </template>
          </van-swipe-cell> -->
        </div>
        <div class="no-data" v-else>
          <div class="no-data-img">
            <img
              :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/no_data.png')"
              :class="uiStyleFlag === 'UI1B' ? 'vms-img' : ''"
            />
          </div>
          <div class="no-data-text">
            <van-button class="add-btn" type="primary" @click="addHouseHold">
              {{ $t('add') }}
            </van-button>
          </div>
        </div>
      </div>
    </tvt-better-scroll>
    <div class="footer">
      <van-button class="footer-btn" type="primary" :loading="addLoading" @click="handleClick">
        {{ isAdd ? $t('confirm') : $t('delete') }}
      </van-button>
    </div>
    <edit-room-num ref="editRoomNum" :value="roomNum" @confirm="confirmName"></edit-room-num>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import EditRoomNum from './dialog/EditRoomNum'
import { formatPhoneNumber } from '@/utils/common.js'
import {
  queryRoomMembers,
  deleteRoomMember,
  editBuildingRoom,
  delBuildingRoom,
  addRoomMembers,
  addBuildingRoom
} from '@/api/householdManagement.js'
import { mapState, mapMutations } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'addRoom',
  components: {
    NavBar,
    EditRoomNum,
    ThemeImage
  },
  props: {},
  data() {
    return {
      isAdd: true,
      pullingStatus: 0,
      roomId: '',
      buildingId: '',
      loading: false,
      addLoading: false
    }
  },
  mounted() {
    this.buildingId = this.$route.query.buildingId

    // 编辑房间
    if (this.$route.query.roomId) {
      this.isAdd = false
      this.roomId = this.$route.query.roomId
      this.SET_ROOM_NUM(this.$route.query.num)
      this.queryDetail()
    } else {
      // 添加房间
      this.isAdd = true
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('householdManagement', ['roomNum', 'memberList']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('householdManagement', ['SET_ROOM_NUM', 'SET_MEMEBER_LIST', 'DEL_MEMBER']),
    back() {
      this.$router.go(-1)
    },
    editRoomNum() {
      this.$refs.editRoomNum.show = true
    },
    handleClick() {
      if (this.isAdd) {
        this.addRoom()
      } else {
        // 删除
        this.deleteRoom()
      }
    },
    async addRoom() {
      // 先创建房间， 然后用房间id添加住户， 设计如此
      try {
        this.$loading.show()
        this.addLoading = true

        if (!this.roomNum) {
          this.$toast(this.$t('enterRoomName'))
          return
        }

        const { data } = await addBuildingRoom({
          id: this.buildingId,
          roomNo: this.roomNum
        })

        await addRoomMembers({
          roomId: data.roomId,
          memberNames: this.memberList.map(memeber => memeber.memberName)
        })
        this.$toast(this.$t('addSuccess'))
        this.back()
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
        this.addLoading = false
      }
    },
    async deleteRoom() {
      const tips = {
        message: this.$t('deleteConfirm'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }

      try {
        this.$loading.show()

        await this.$dialog.confirm(tips)

        await delBuildingRoom({
          id: this.roomId
        })

        this.$toast(this.$t('deleteSuccess'))
        this.back()
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
      }
    },
    addHouseHold() {
      if (this.memberList.length >= 5) {
        this.$toast(this.$t('memberInMax'))
        return
      }

      if (this.isAdd) {
        this.$router.push({
          path: '/household/addRoomMember'
        })
      } else {
        this.$router.push({
          path: '/household/addRoomMember',
          query: {
            id: this.roomId
          }
        })
      }
    },
    editHouseHold(member) {
      if (this.isAdd) {
        this.$router.push({
          path: '/household/addRoomMember',
          query: {
            id: this.roomId,
            name: member.memberName
          }
        })
      } else {
        // 已注册的用户不做编辑住户
      }
    },
    async deleteHouseHold(member, index) {
      const tips = {
        message: this.$t('deleteConfirm'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }

      try {
        await this.$dialog.confirm(tips)
        this.$loading.show()

        if (this.isAdd) {
          this.DEL_MEMBER(index)
        } else {
          await deleteRoomMember({
            id: member.buildingMemberId
          })

          const { data } = await queryRoomMembers({ id: this.roomId })

          this.SET_MEMEBER_LIST(data)
        }

        this.$toast(this.$t('deleteSuccess'))
      } catch (error) {
        console.error(error)
        return false
      } finally {
        this.$loading.hide()
      }
    },
    async confirmName(value) {
      try {
        this.$loading.show()

        if (this.isAdd) {
          this.SET_ROOM_NUM(value)
        } else {
          await editBuildingRoom({
            roomId: this.roomId,
            roomNo: value
          })
          this.SET_ROOM_NUM(value)
          this.$toast(this.$t('changeSuccessfully'))
        }

        this.$refs.editRoomNum.show = false
      } catch (error) {
        console.error(error)
        return false
      } finally {
        this.$loading.hide()
      }
    },
    async pullingDown(callback) {
      // 新增不刷新
      if (this.isAdd) {
        callback && callback()
        return
      }
      await this.queryDetail()
      callback && callback()
    },
    async queryDetail() {
      try {
        this.$loading.show()
        this.SET_MEMEBER_LIST([])
        this.loading = true
        const { data } = await queryRoomMembers({ id: this.roomId })

        this.SET_MEMEBER_LIST(data)
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
        this.loading = false
      }
    },
    formatPhoneNumber
  }
}
</script>
<style lang="scss" scoped>
.household-manangement-wrapper {
  height: 100%;
  overflow: auto;
  padding-bottom: 80px;
  box-sizing: border-box;
  .tvt-better-scroll {
    padding-top: 10px;
    height: calc(100% - 44px);
    box-sizing: border-box;

    .household-item {
      ::v-deep .van-cell__title {
        height: 24px;
      }

      ::v-deep .van-cell__value {
        height: 24px;
      }
      padding-top: 12px;
      padding-bottom: 12px;
      &::after {
        border: none;
      }

      .right-value {
        display: inline-block;
        width: 100%;
      }
    }

    .room-container {
      padding: 10px 0;

      .room-header {
        display: flex;
        justify-content: space-between;
        font-weight: 400;
        font-size: var(--font-size-body2-size, 14px);
        line-height: 24px;
        padding: 10px 6px 10px 16px;
      }

      .room-add {
        padding: 0 15px;
      }

      .room-list {
        .room-item {
          height: 50px;
          line-height: 30px;

          &::v-deep .van-cell__right-icon {
            line-height: 30px;
          }
        }
        .room-name {
          display: inline-block;
          width: 90%;
        }
        .room-member-name {
          display: inline-block;
          width: 90%;
        }
        .swipe-right-btn {
          height: 100%;

          &::v-deep .van-button__text {
            height: 24px;
          }
        }
        .refuse-img {
          width: 24px;
          height: 24px;
        }
        &::v-deep .van-cell-right-icon {
          position: relative;
          top: 3px;
        }
      }
    }
  }

  .footer {
    position: fixed;
    bottom: 20px;
    width: 100%;
    // padding: 20px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .footer-btn {
      width: 296px;
      border-radius: 23px;
      line-height: 46px;
      text-align: center;
    }
  }

  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 235px;
        height: 211px;
      }
      .vms-img {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-text {
      width: 300px;
      margin: auto;
      text-align: center;

      .add-btn {
        padding: 0 35px;
        border-radius: 23px;
        line-height: 46px;
        text-align: center;
      }
    }
  }
}
</style>
