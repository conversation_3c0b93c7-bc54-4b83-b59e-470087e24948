<template>
  <div class="target-face-manangement-wrapper">
    <nav-bar v-if="!showSearchName" @clickLeft="back" class="target-face-nav-bar">
      <template #right>
        <img src="@/assets/img/common/filter.png" class="nav-bar-icon" @click="filterList" />
        <img src="@/assets/img/common/search.png" class="nav-bar-icon" @click="startSeachName" />
        <img src="@/assets/img/common/add_circle.png" class="nav-bar-icon" @click="addTargetFace" />
      </template>
    </nav-bar>
    <search-name v-else @search="handleSearchName" @cancel="cancelSearchName" />
    <tvt-better-scroll
      class="tvt-better-scroll"
      ref="betterScrollRef"
      :showBottomCompleteText="false"
      @pullingUp="pullingUp"
      @pullingDown="pullingDown"
      :pullingStatus="pullingStatus"
    >
      <template v-if="loading || targetFaceList.length">
        <van-swipe-cell v-for="item in targetFaceList" :key="item.personID">
          <!-- 阻止页面的点击穿透 -->
          <div @mouseup="editTargetFace(item)">
            <van-cell class="target-item" is-link>
              <template #title>
                <img :src="item.faceImg || defaultFaceImg" class="target-face" />
                <div class="target-info text-over-ellipsis">
                  <span class="target-name text-over-ellipsis">{{ $t('targetName') }}: {{ item.name }}</span>
                  <span class="target-name text-over-ellipsis">{{ $t('targetType') }}: {{ $t(item.listType) }}</span>
                  <span class="target-name text-over-ellipsis">{{ $t('cardId') }}: {{ item.cardIds.join('、') }}</span>
                </div>
              </template>
            </van-cell>
          </div>

          <template #right>
            <van-button square type="danger" class="swipe-right-btn" @click="deleteTargetFace(item)">
              <img
                class="refuse-img"
                alt=""
                :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/delete.png')"
              />
            </van-button>
          </template>
        </van-swipe-cell>
      </template>

      <div class="no-data" v-else>
        <div class="no-data-img">
          <img
            :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/no_data.png')"
            :class="uiStyleFlag === 'UI1B' ? 'vms-img' : ''"
          />
        </div>
      </div>
    </tvt-better-scroll>
    <van-popup v-model="showTypeSearch" position="bottom">
      <div class="target-face-type-list">
        <div class="target-face-type disabled">{{ $t('filter') }}</div>
        <div
          v-for="item in listTypeArray"
          :key="item"
          class="target-face-type"
          :class="{ selected: item === listType }"
          @click="filterType(item)"
        >
          {{ $t(item) }}
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { mapState, mapActions, mapMutations } from 'vuex'
import NavBar from '@/components/NavBar'
import { getUrlQuery } from '@/utils/common'

import { appClose } from '@/utils/appbridge'
import SearchName from './SearchName.vue'

export default {
  name: 'TargetFaceList',
  components: {
    NavBar,
    SearchName
  },
  props: {},
  data() {
    return {
      listParams: {
        pageNum: 0,
        pageSize: 10
      },
      pullingStatus: 0,
      loading: false,
      showTypeSearch: false,
      showSearchName: false,
      listTypeArray: ['all', 'strangerList', 'whiteList', 'blackList', 'admin'],
      listType: 'all',
      defaultFaceImg: require('@/assets/img/common/default_avatar.png'),
      searchName: ''
    }
  },
  mounted() {
    const json = getUrlQuery(window.location.href)
    let { devId } = json

    this.SET_DEV_ID(devId)
    this.getList()
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('targetFace', ['targetFaceList']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapActions('targetFace', ['queryTargetFaceList', 'delTargetFace']),
    ...mapMutations('targetFace', ['SET_TARGET_FACE_LIST', 'SET_DEV_ID']),
    back() {
      appClose()
    },
    filterList() {
      this.showTypeSearch = true
    },
    startSeachName() {
      this.searchName = ''
      this.showSearchName = true
    },
    cancelSearchName() {
      this.showSearchName = false
      this.listParams.pageNum = 0
      if (this.searchName) {
        this.searchName = ''
        this.getList()
      }
    },
    handleSearchName(value) {
      this.listParams.pageNum = 0
      this.searchName = value

      this.getList()
    },
    addTargetFace() {
      this.$router.push({
        name: 'targetFaceDetail',
        query: {
          personId: String(-1)
        }
      })
    },
    editTargetFace(item) {
      this.$router.push({
        name: 'targetFaceDetail',
        query: {
          personId: String(item.personID)
        }
      })
    },
    async deleteTargetFace(item) {
      const tips = {
        message: this.$t('confirmDeletePerson'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }

      try {
        await this.$dialog.confirm(tips)

        await this.delTargetFace({
          personID: item.personID
        })

        this.$toast(this.$t('deleteSuccess'))
        this.pullingDown()
      } catch (error) {
        console.error(error)
      }
    },
    pullingUp(callback) {
      this.getList({ type: 'up', callback })
    },
    pullingDown(callback) {
      this.listParams.pageNum = 0
      this.getList({ type: 'down', callback })
    },
    filterType(listType) {
      this.listType = listType
      this.showTypeSearch = false

      this.listParams.pageNum = 0
      this.getList()
    },

    async getList({ callback, type = 'down' } = {}) {
      try {
        this.$loading.show()
        this.loading = true

        const params = {
          ...this.listParams,
          name: this.searchName
        }

        if (this.listType !== 'all') {
          params.listType = this.listType
        }

        const { total, records } = await this.queryTargetFaceList(params)
        let dataList = this.targetFaceList

        if (type === 'down') {
          dataList = []
        }

        dataList = dataList.concat(records)
        this.SET_TARGET_FACE_LIST(dataList)
        this.listParams.pageNum++

        this.pullingStatus = total ? (dataList.length === total ? 0 : 2) : 2
      } catch (error) {
        console.error(error)
        this.SET_TARGET_FACE_LIST([])
        this.queryParams.pageNum = 0
      } finally {
        this.$loading.hide()
        this.loading = false
        if (callback) {
          callback()
        } else {
          this.$nextTick(() => {
            this.$refs.betterScrollRef && this.$refs.betterScrollRef.refresh()
          })
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.target-face-manangement-wrapper {
  height: 100%;
  overflow: auto;
  .tvt-better-scroll {
    padding: 10px 0;
    height: calc(100% - 64px);
  }
  ::v-deep .target-face-nav-bar {
    .nav-bar-left .van-icon-arrow-left {
      font-size: var(--font-size-h5-size, 18px);
    }
    .nav-bar-center {
      font-size: var(--font-size-body1-size, 16px);
    }
    .nav-bar-right {
      padding-right: 10px;
    }
    .nav-bar-icon {
      width: 24px;
      height: 24px;

      &:not(:last-child) {
        margin-right: 16px;
      }
    }
  }
  .target-item {
    ::v-deep .van-cell__title {
      display: flex;
    }
    ::v-deep .van-cell__right-icon {
      line-height: 80px;
    }
    .target-face {
      display: inline-block;
      width: 60px;
      height: 80px;
      margin-right: 10px;
    }
    .target-info {
      flex: 1;
      width: 0;
      display: inline-flex;
      flex-direction: column;
      justify-content: space-between;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: var(--font-size-text-size, 12px);
      color: var(--icon-color-primary, #393939);
    }
  }
  .swipe-right-btn {
    width: 70px;
    height: 100px;
    background-color: var(--error-bg-color-default, #ff5656);
    border-color: var(--error-bg-color-default, #ff5656);
  }
  .target-face-type-list {
    padding-bottom: 23px;
    .target-face-type {
      height: 46px;
      box-shadow: inset 0 -1px 0 0 var(--outline-color-primary, #ebebeb);
      line-height: 46px;
      font-weight: 400;
      font-size: var(--font-size-body2-size, 14px);
      color: var(--icon-color-primary, #393939);
      text-align: center;

      &.disabled {
        background: #f5f8fe;
      }
      &.selected {
        color: var(--text-color-brand, #00baff);
      }
    }
  }

  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 135px;
      img {
        width: 235px;
        height: 211px;
      }
      .vms-img {
        width: 120px;
        height: 123px;
      }
    }
  }
}
</style>
