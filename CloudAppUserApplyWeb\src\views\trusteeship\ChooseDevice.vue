<template>
  <div class="trusteeship-choose-device">
    <nav-bar @clickLeft="back"></nav-bar>
    <tvt-better-scroll
      class="tvt-better-scroll"
      @pullingUp="pullingUp"
      @pullingDown="pullingDown"
      :pullingStatus="pullingStatus"
    >
      <div class="device-trusteeship" v-if="dataList.length">
        <!-- <check :list="dataList" @curShow="curShow"></check> -->
        <trusteeship-check :list="dataList" @curShow="curShow"></trusteeship-check>
      </div>
      <div class="no-data" v-else>
        <div class="no-data-img">
          <img
            :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/no_data.png')"
            :class="uiStyleFlag === 'UI1B' ? 'vms-img' : ''"
          />
        </div>
        <div class="no-data-text">{{ $t('noAvaiableDevice') }}</div>
      </div>
    </tvt-better-scroll>
    <div class="footer" v-if="dataList.length">
      <div class="footer-btn" @click="finish">{{ $t('finish') }}</div>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { appSetTitle, appClose } from '@/utils/appbridge'
import { getUrlQuery } from '@/utils/common'
import { mapState, mapMutations } from 'vuex'
import { AUTH_LIST } from '@/utils/options.js'
import { deviceTrusteeshipsList, deviceTrusteeshipsCreate } from '@/api/trusteeship.js'
// import Check from '@/components/Check.vue'
import TrusteeshipCheck from '@/components/TrusteeshipCheck'
export default {
  name: 'chooseDevice',
  components: {
    NavBar,
    // Check
    TrusteeshipCheck
  },
  data() {
    return {
      pageContent: navigator.userAgent,
      authListOptions: AUTH_LIST(),
      pageNum: 1,
      pageSize: 9999,
      dataList: [],
      pullingStatus: 0
    }
  },
  mounted() {
    appSetTitle(this.$t('chooseDevice'))
    let json = getUrlQuery(window.location.href)
    console.log(json)
    if (this.availableList && this.availableList.length) {
      this.dataList = this.availableList
    } else {
      this.$nextTick(() => {
        this.getList({ type: 'down' })
      })
    }
  },
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    ...mapState('trusteeship', ['availableList']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('trusteeship', ['SET_AVAILABLE_LIST']),
    back() {
      this.$router.go(-1)
    },
    pullingUp(callback) {
      console.log(callback, 'callback')
      this.getList({ type: 'up', callback })
    },
    pullingDown(callback) {
      this.pageNum = 1
      this.getList({ type: 'down', callback })
    },
    // 查询已托管的设备列表
    getList(param) {
      let that = this
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        type: 0 // 0: 可托管设备； 1：已发起托管设备；
      }
      this.$loading.show()
      deviceTrusteeshipsList(params)
        .then(({ data }) => {
          this.$loading.hide()
          let resData = data || []
          that.dataList = that.dealInitData(resData)
          // 新加
          that.SET_AVAILABLE_LIST(that.dataList)
          if (param && param.callback) param.callback()
        })
        .catch(error => {
          this.$loading.hide()
          if (error.basic && error.basic.code === 32018) {
            setTimeout(() => {
              appClose()
            }, 1000)
          }
          if (param && param.callback) param.callback()
        })
    },
    // 初始化数据显示
    dealInitData(list) {
      let that = this
      let newList = []
      list.forEach(item => {
        let authList = []
        that.authListOptions.forEach(v => {
          authList.push({ label: v.value, value: v.value, flag: true }) // 默认勾选全部权限，配置权限默认勾选置灰不可取消
        })
        let newItem = {
          ...item,
          flag: item.flag ? item.flag : false,
          show: item.show ? item.show : false,
          authList,
          effectiveTime: 0 //0表示永久
        }
        newList.push(newItem)
      })
      return newList
    },
    // 处理结果 传后台的数据
    dealResData(list) {
      let newList = []
      list.forEach(item => {
        let authList = []
        item.authList.forEach(v => {
          if (v.flag) {
            authList.push(v.value)
          }
        })
        let newItem = {
          ...item,
          authList
        }
        delete newItem['flag']
        delete newItem['show']
        newList.push(newItem)
      })
      return newList
    },
    finish() {
      // 校验 至少选择一个设备
      let checkDevice = []
      this.dataList.forEach(item => {
        if (item.flag) {
          checkDevice.push(item)
        }
      })
      if (checkDevice.length === 0) {
        this.$toast(this.$t('leastChoose'))
        return false
      }
      // 处理选中的数据
      let resData = this.dealResData(checkDevice)
      console.log(resData, '选中托管设备的数据')
      this.$loading.show()
      deviceTrusteeshipsCreate(resData)
        .then(res => {
          this.$loading.hide()
          console.log(res, '发起托管的返回')
          if (res.basic.code == 200) {
            this.$dialog
              .alert({ message: this.$t('confirmTrusteeshipTip'), confirmButtonText: this.$t('confirm') })
              .then(() => {
                this.back()
              })
          }
        })
        .catch(error => {
          this.$loading.hide()
          if (!error.basic) return
          if (error.basic.code === 32018) {
            setTimeout(() => {
              appClose()
            }, 1000)
          } else if (error.basic.code === 32019 || error.basic.code === 32021) {
            this.back()
          } else if (error.basic.code === 32022) {
            let devName = error.basic.msg ? JSON.parse(error.basic.msg).devName : ''
            this.$toast(this.$t('errorCode[32022]', [devName]))
          }
        })
    },
    curShow: function (item) {
      item.show = !item.show
    }
  }
}
</script>
<style lang="scss" scoped>
.trusteeship-choose-device {
  height: calc(100% - 70px);
  overflow: auto;
  .tvt-better-scroll {
    height: 100%;
  }
  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 135px;
      img {
        width: 235px;
        height: 211px;
      }
      .vms-img {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-text {
      width: 300px;
      margin: auto;
      text-align: center;
      color: var(--text-color-placeholder, #8f8e93);
    }
  }
}
</style>
