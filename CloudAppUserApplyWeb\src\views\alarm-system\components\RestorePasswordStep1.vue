<template>
  <div class="restore-password-step">
    <div class="restore-title">Restore Password</div>
    <div class="restore-subtitle">Input the Email address you used for registering</div>

    <div class="restore-form">
      <input type="email" class="restore-input" v-model="email" placeholder="Email" />
    </div>

    <div class="restore-actions">
      <van-button class="action-btn" @click="handleNext">Next</van-button>
      <van-button class="action-btn cancel-btn" @click="handleCancel">Cancel</van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RestorePasswordStep1',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      email: ''
    }
  },
  methods: {
    handleNext() {
      if (!this.email) {
        this.$toast('Please enter your email address')
        return
      }

      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(this.email)) {
        this.$toast('Please enter a valid email address')
        return
      }

      this.$emit('next', { email: this.email })
    },
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.restore-password-step {
  padding: 20px;
  background-color: #333;

  .restore-title {
    font-size: 20px;
    color: #fff;
    margin-bottom: 16px;
  }

  .restore-subtitle {
    font-size: 14px;
    color: #fff;
    margin-bottom: 20px;
  }

  .restore-form {
    margin-bottom: 30px;

    .restore-input {
      width: 100%;
      height: 40px;
      background-color: #fff;
      border: none;
      padding: 0 12px;
      font-size: 16px;
      box-sizing: border-box;
    }
  }

  .restore-actions {
    display: flex;
    justify-content: space-between;

    .action-btn {
      width: 48%;
      height: 40px;
      background-color: #999;
      border: none;
      border-radius: 4px;
      color: #fff;
      font-size: 16px;
    }

    .cancel-btn {
      background-color: #777;
    }
  }
}
</style>
