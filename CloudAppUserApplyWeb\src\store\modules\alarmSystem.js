export default {
  namespaced: true,
  state: () => ({
    alarmSystemType: 'Tyco', // 报警系统类型 Tyco/Risco/Pima
    userInfo: null, // 用户信息
    listData: [] // 列表数据
  }),
  getters: {},
  mutations: {
    SET_ALARM_SYSTEM_TYPE(state, data) {
      state.alarmSystemType = data
    },
    SET_USER_INFO(state, data) {
      state.userInfo = data
    },
    CLEAR_USER_INFO(state) {
      state.userInfo = null
    },
    SET_LIST_DATA(state, data) {
      state.listData = data
    },
    RESET_LIST_DATA(state) {
      state.listData = []
    }
  },
  actions: {}
}
