<template>
  <div class="alarm-login">
    <nav-bar title="Alarm System" @clickLeft="goBack" />
    <div class="alarm-login-content">
      <div class="login-title">
        {{ alarmSystemType === 'pima' ? 'Configure account settings' : 'Login' }}
      </div>

      <div class="server-url" v-if="alarmSystemType === 'tyco'">
        {{ loginForm.server }}
      </div>

      <div class="login-form tyco-form">
        <!-- Tyco specific inputs -->
        <template v-if="alarmSystemType === 'tyco'">
          <div class="form-item">
            <div class="form-label">E-Mail</div>
            <input type="email" class="form-input" v-model="loginForm.email" />
            <div class="error-message" v-if="errors.email">{{ errors.email }}</div>
          </div>
          <div class="form-item">
            <div class="form-label">Password</div>
            <input :type="showPassword ? 'text' : 'password'" class="form-input" v-model="loginForm.password" />
            <div class="error-message" v-if="errors.password">{{ errors.password }}</div>
          </div>
        </template>

        <!-- Risco specific inputs -->
        <template v-else-if="alarmSystemType === 'risco'">
          <div class="form-item">
            <input type="text" class="form-input" v-model="loginForm.username" placeholder="User Name" />
            <div class="error-message" v-if="errors.username">{{ errors.username }}</div>
          </div>
          <div class="form-item">
            <input
              :type="showPassword ? 'text' : 'password'"
              class="form-input"
              v-model="loginForm.password"
              placeholder="Password"
            />
            <div class="error-message" v-if="errors.password">{{ errors.password }}</div>
          </div>
        </template>

        <!-- Pima specific inputs -->
        <template v-else-if="alarmSystemType === 'pima'">
          <div class="form-item">
            <input type="text" class="form-input" v-model="loginForm.name" placeholder="Name" />
            <div class="error-message" v-if="errors.name">{{ errors.name }}</div>
          </div>
          <div class="form-item">
            <input type="email" class="form-input" v-model="loginForm.email" placeholder="Email" />
            <div class="error-message" v-if="errors.email">{{ errors.email }}</div>
          </div>
          <div class="form-item">
            <input type="tel" class="form-input" v-model="loginForm.phone" placeholder="Phone Number" />
            <div class="error-message" v-if="errors.phone">{{ errors.phone }}</div>
          </div>
        </template>
      </div>

      <!-- Common actions block with conditional styles -->
      <div
        :class="{
          'login-actions-tyco': alarmSystemType === 'tyco',
          'login-actions-common': alarmSystemType !== 'tyco'
        }"
      >
        <van-button :type="alarmSystemType === 'tyco' ? 'danger' : 'default'" block @click="handleLogin">
          Login
        </van-button>
        <div
          :class="{
            'action-links': alarmSystemType === 'tyco',
            'forgot-password-centered': alarmSystemType !== 'tyco'
          }"
        >
          <span class="register" @click="handleRegister" v-if="alarmSystemType === 'tyco'">Register</span>
          <span class="forgot-password" @click="handleForgotPassword">
            {{ alarmSystemType === 'tyco' ? 'Forgot Password' : 'forgot password?' }}
          </span>
        </div>
      </div>
    </div>

    <restore-password v-model="showRestorePassword" @success="handleRestoreSuccess" />
  </div>
</template>

<script>
import NavBar from '@/components/NavBar.vue'
import { mapState } from 'vuex'
import RestorePassword from './components/RestorePassword.vue'

export default {
  name: 'AlarmLogin',
  components: {
    NavBar,
    RestorePassword
  },
  data() {
    return {
      loginForm: {
        server: '',
        email: '',
        password: '',
        username: '',
        name: '',
        phone: ''
      },
      errors: {
        server: '',
        email: '',
        password: '',
        username: '',
        name: '',
        phone: ''
      },
      showPassword: false,
      showRestorePassword: false
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('alarmSystem', ['alarmSystemType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  created() {
    const systemType = this.$route.query.systemType
    if (systemType) {
      this.$store.commit('alarmSystem/SET_ALARM_SYSTEM_TYPE', systemType.toLowerCase())
    }
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    validateForm() {
      let isValid = true
      // 重置错误信息
      this.errors = {
        server: '',
        email: '',
        password: '',
        username: '',
        name: '',
        phone: ''
      }

      if (this.alarmSystemType === 'tyco') {
        // Tyco系统验证
        if (!this.loginForm.server.trim()) {
          this.errors.server = 'Server address is required'
          isValid = false
        }
        if (!this.loginForm.email.trim()) {
          this.errors.email = 'Email is required'
          isValid = false
        } else {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          if (!emailRegex.test(this.loginForm.email)) {
            this.errors.email = 'Please enter a valid email address'
            isValid = false
          }
        }
        if (!this.loginForm.password) {
          this.errors.password = 'Password is required'
          isValid = false
        }
      } else if (this.alarmSystemType === 'risco') {
        // Risco系统验证
        if (!this.loginForm.username.trim()) {
          this.errors.username = 'Username is required'
          isValid = false
        }
        if (!this.loginForm.password) {
          this.errors.password = 'Password is required'
          isValid = false
        }
      } else if (this.alarmSystemType === 'pima') {
        // Pima系统验证
        if (!this.loginForm.name.trim()) {
          this.errors.name = 'Name is required'
          isValid = false
        }
        if (!this.loginForm.email.trim()) {
          this.errors.email = 'Email is required'
          isValid = false
        } else {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          if (!emailRegex.test(this.loginForm.email)) {
            this.errors.email = 'Please enter a valid email address'
            isValid = false
          }
        }
        if (!this.loginForm.phone.trim()) {
          this.errors.phone = 'Phone number is required'
          isValid = false
        }
      }

      return isValid
    },
    handleLogin() {
      // 验证表单
      if (!this.validateForm()) {
        return
      }

      // 这里应该调用登录API
      this.$toast.loading({
        message: 'Logging in...',
        forbidClick: true,
        duration: 0
      })

      // 模拟登录请求
      setTimeout(() => {
        this.$toast.clear()

        // 保存用户信息到store
        const userInfo = {
          email: this.loginForm.email,
          username: this.loginForm.username,
          name: this.loginForm.name,
          phone: this.loginForm.phone,
          systemType: this.alarmSystemType
        }
        this.$store.commit('alarmSystem/SET_USER_INFO', userInfo)

        // 登录成功后跳转到Account Details页面
        this.$router.push('/alarmSystem/accountDetails')
      }, 1500)
    },
    handleForgotPassword() {
      this.showRestorePassword = true
    },
    handleRestoreSuccess() {
      // 密码重置成功后可以做一些操作，比如提示用户使用新密码登录
      this.$toast('Password has been reset, please login with your new password')
    },
    handleRegister() {
      // 处理注册逻辑
      this.$router.push('/alarm-system/register')
    }
  }
}
</script>

<style lang="scss" scoped>
.alarm-login {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #333;

  &-content {
    flex: 1;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
  }

  .login-title {
    font-size: 24px;
    color: #fff;
    margin-bottom: 16px;
    text-align: center;
  }

  .server-url {
    color: #fff;
    font-size: 14px;
    text-align: center;
    margin-bottom: 30px;
    opacity: 0.8;
  }

  .tyco-form {
    .form-item {
      margin-bottom: 20px;

      .form-label {
        font-size: 16px;
        color: #fff;
        margin-bottom: 8px;
      }

      .form-input {
        width: 100%;
        height: 44px;
        background-color: #fff;
        border: none;
        border-radius: 4px;
        padding: 0 12px;
        font-size: 16px;
        color: #333;
        box-sizing: border-box;

        &::placeholder {
          color: #999;
        }
      }

      .error-message {
        color: #ff4d4f;
        font-size: 12px;
        margin-top: 4px;
      }
    }
  }

  .login-actions-tyco {
    margin-top: auto;
    padding-bottom: 20px;

    .van-button {
      height: 44px;
      font-size: 16px;
      background-color: #ff0000;
      border: none;
    }

    .action-links {
      display: flex;
      justify-content: space-between;
      margin-top: 16px;
      color: #fff;
      font-size: 14px;

      .register,
      .forgot-password {
        cursor: pointer;
        opacity: 0.8;

        &:hover {
          opacity: 1;
        }
      }
    }
  }

  .login-actions-common {
    margin-top: auto;
    padding-bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .van-button {
      width: 100%;
      height: 44px;
      font-size: 16px;
      background-color: #999; /* Grey button */
      border: none;
      border-radius: 4px;
      color: #fff;
    }
  }

  .forgot-password-centered {
    margin-top: 16px;
    text-align: center;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
    opacity: 0.8;

    &:hover {
      opacity: 1;
    }
  }
}
</style>
