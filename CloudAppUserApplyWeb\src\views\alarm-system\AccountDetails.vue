<template>
  <div class="account-details">
    <nav-bar title="Alarm System" @clickLeft="goBack" />
    <div class="account-details-content">
      <div class="account-title">Account Details</div>

      <div class="account-actions">
        <van-button type="default" block class="action-button log-out-button" @click="handleLogOut">
          Log Out
        </van-button>

        <van-button type="default" block class="action-button panel-list-button" @click="handlePanelList">
          Panel List
        </van-button>
      </div>
    </div>

    <!-- 确认对话框 -->
    <van-dialog
      v-model="showLogoutDialog"
      title="Confirm Logout"
      message="Are you sure you want to log out?"
      show-cancel-button
      @confirm="confirmLogout"
      @cancel="showLogoutDialog = false"
    />
  </div>
</template>

<script>
import NavBar from '@/components/NavBar.vue'
import { mapState } from 'vuex'

export default {
  name: 'AccountDetails',
  components: {
    NavBar
  },
  data() {
    return {
      showLogoutDialog: false
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('alarmSystem', ['alarmSystemType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    handleLogOut() {
      this.showLogoutDialog = true
    },
    handlePanelList() {
      // 跳转到面板列表页面
      this.$router.push('/alarmSystem/panelList')
    },
    confirmLogout() {
      this.showLogoutDialog = false

      // 显示加载提示
      this.$toast.loading({
        message: 'Logging out...',
        forbidClick: true,
        duration: 1000
      })

      // 模拟登出操作
      setTimeout(() => {
        this.$toast.clear()

        // 清除用户登录状态
        this.$store.commit('alarmSystem/CLEAR_USER_INFO')

        // 跳转到登录页面
        this.$router.replace('/alarmSystem/chooseSystem')

        this.$toast.success('Logged out successfully')
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
.account-details {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #333;

  &-content {
    flex: 1;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
  }

  .account-title {
    font-size: 18px;
    color: #fff;
    margin-bottom: 40px;
    text-align: center;
  }

  .account-actions {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 20px;

    .action-button {
      height: 44px;
      border-radius: 4px;
      font-size: 16px;
      font-weight: 500;
      border: none;

      &.log-out-button {
        background-color: #666;
        color: #fff;

        &:active {
          background-color: #555;
        }
      }

      &.panel-list-button {
        background-color: #666;
        color: #fff;

        &:active {
          background-color: #555;
        }
      }
    }
  }
}

// 自定义对话框样式
::v-deep .van-dialog {
  .van-dialog__header {
    color: #333;
    font-weight: 600;
  }

  .van-dialog__message {
    color: #666;
    text-align: center;
  }

  .van-dialog__footer {
    .van-button {
      border: none;

      &.van-button--default {
        color: #666;
      }

      &.van-button--primary {
        background-color: #1989fa;
      }
    }
  }
}
</style>
