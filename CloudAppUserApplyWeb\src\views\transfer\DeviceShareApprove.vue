<template>
  <div class="detail-wrapper">
    <nav-bar @clickLeft="clickLeft" :title="title"></nav-bar>
    <tvt-better-scroll class="tvt-better-scroll" @pullingDown="pullingDown" :pullingStatus="pullingStatus">
      <div class="detail-content">
        <div class="card">
          <installer-info :data="detail" />
        </div>
        <!-- 设备分享申请 -->
        <div class="hosting-tip">
          {{ tips }}
        </div>
        <div class="gray-gap"></div>
        <div class="site-list">
          <div class="site">
            <div class="site-header">
              <div class="site-header-left" @click="siteDescVisible = true">
                <img src="@/assets/img/common/trusteeship/site.png" class="site-icon" />
                <div class="site-label">{{ $t('site') }}</div>
                <img src="@/assets/img/common/trusteeship/question.svg" class="site-desc-icon" />
              </div>
              <div class="site-tool">
                <div class="site-name text-over-ellipsis">
                  {{ currentSite.siteName }}
                </div>
              </div>
            </div>
            <div v-if="currentSite.deviceTrusts?.length === 0" class="empty-list">
              <img src="@/assets/img/common/no_data_max.png" class="empty-img" />
              <span class="empty-info">
                {{ $t('noDeviceHosting') }}
              </span>
            </div>
            <div class="device-list">
              <div class="device-title">
                {{ $t('shareDevice') }}
              </div>
              <div class="device-item" v-for="device in currentSite.deviceTrusts" :key="device.sn">
                <div class="device-icon-wrapper"></div>
                <div class="device-info">
                  <div :class="['device-name']">
                    {{ device.devName }}
                  </div>
                  <div v-if="device.permissionTr" class="device-permission">
                    {{ device.permissionTr }}
                  </div>
                </div>
              </div>
              <van-field
                v-model="currentSite.remark"
                rows="6"
                disabled
                autosize
                type="textarea"
                class="host-remark"
                :placeholder="$t('remarkPlaceholder')"
              />
            </div>
          </div>
        </div>
        <div v-if="currentSite.status !== APPLICATION_STATUS.canceled" class="padding-bottom"></div>
      </div>
    </tvt-better-scroll>

    <div v-if="currentSite.status !== APPLICATION_STATUS.canceled" class="footer-container">
      <template v-if="currentSite.status === APPLICATION_STATUS.pending">
        <van-button class="footer-btn" @click="cancelShareVisible = true">
          {{ $t('reject') }}
        </van-button>
        <van-button class="footer-btn primary" type="primary" @click="approveApplication">
          {{ $t('agree') }}
        </van-button>
      </template>
      <van-button v-else class="footer-btn primary disabled" type="primary" disabled>
        {{ disabledBtnText }}
      </van-button>
    </div>
    <site-desc-popup :visible.sync="siteDescVisible" />
    <cancel-device-share-popup :visible.sync="cancelShareVisible" @submit="rejectApplication" />
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import InstallerInfo from '@/views/max-hosting/components/InstallerInfo'
import { appBack } from '@/utils/appbridge'

import { formatInstallerInfo } from '@/views/max-hosting/common'
import { getInstallerInfo } from '@/api/maxHosting'
import { getDeviceShareInfo, handleDeviceShareInfo } from '@/api/transfer'
import SiteDescPopup from '@/views/max-hosting/components/SiteDescPopup'
import CancelDeviceSharePopup from './components/CancelDeviceSharePopup'
import { updateMessageStatus } from '@/api/message'

const applicationStatus = {
  pending: 0,
  approved: 1,
  rejected: 2,
  canceled: 3,
  outdated: 4,
  invalid: 5
}

export default {
  name: 'deviceShareApprove',
  components: {
    NavBar,
    InstallerInfo,
    SiteDescPopup,
    CancelDeviceSharePopup
  },
  data() {
    return {
      pullingStatus: 0,
      detail: {},
      orderId: '',
      siteId: '',
      APPLICATION_STATUS: applicationStatus,
      siteDescVisible: false,
      cancelShareVisible: false,
      currentSite: {
        status: applicationStatus.canceled
      },
      currentDevice: {},
      installerUserId: '',
      orderType: 'site',
      action: '',
      loading: true,
      msgId: ''
    }
  },
  computed: {
    title() {
      if (this.loading) {
        return ' '
      }

      if (this.action === 'apply') {
        return this.$t('shareDeliceryTitle')
      } else if (this.action === 'cancel') {
        return this.$t('shareDeliceryCancelTitle')
      }
      return ''
    },
    tips() {
      if (this.loading) {
        return ' '
      }
      if (this.action === 'apply') {
        return this.$t('deviceShareDeliveryTips')
      } else if (this.action === 'cancel') {
        return this.$t('shareCancelTips')
      }
      return ''
    },
    disabledBtnText() {
      if (this.currentSite.status === applicationStatus.approved) {
        return this.$t('agreed')
      }

      if (this.currentSite.status === applicationStatus.rejected) {
        return this.$t('rejected')
      }

      if (this.currentSite.status === applicationStatus.outdated) {
        return this.$t('expired')
      }

      if (this.currentSite.status === applicationStatus.invalid) {
        return this.$t('invalid')
      }

      return ''
    }
  },
  created() {
    const { orderId, siteId } = this.$route.params
    const { installerUserId, action, msgId } = this.$route.query

    this.msgId = msgId
    if (orderId && siteId) {
      this.orderId = orderId
      this.siteId = siteId
      this.installerUserId = installerUserId
      this.action = action

      this.refreshRequest()
    } else {
      this.orderId = ''
      this.siteId = ''
      this.installerUserId = ''
    }
  },
  methods: {
    formatInstallerInfo,
    async getDetail() {
      const { data: detail } = await getInstallerInfo({
        userId: this.installerUserId
      })

      if (detail) {
        this.detail = formatInstallerInfo(detail)
      }
    },
    // 刷新当前页面请求
    async refreshRequest() {
      this.getDetail()

      const params = {
        ticketId: this.orderId,
        siteId: this.siteId
      }

      try {
        this.loading = true
        const {
          data: { site, deviceTransferOutVos, serviceTicket }
        } = await getDeviceShareInfo(params)

        const currentSite = site || {}
        currentSite.deviceTrusts =
          deviceTransferOutVos.map(item => {
            let permissionTr = ''

            if (
              [applicationStatus.rejected, applicationStatus.invalid, applicationStatus.outdated].includes(
                serviceTicket.status
              )
            ) {
              permissionTr = '--'
            } else if (serviceTicket.status === applicationStatus.approved) {
              permissionTr = this.$t('config')
            }

            return {
              ...item,
              permissionTr
            }
          }) || []
        currentSite.remark = serviceTicket?.remark || ''
        if (serviceTicket?.status === applicationStatus.canceled) {
          this.action = 'cancel'
        }
        currentSite.status = serviceTicket?.status || 0
        this.currentSite = currentSite
      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    async pullingUp(callback) {
      // 刷新
      this.refreshRequest()
      if (callback) callback()
    },
    async pullingDown(callback) {
      // 刷新
      await this.refreshRequest()

      if (callback) callback()
    },
    clickLeft() {
      appBack()
    },
    async rejectApplication() {
      const params = {
        siteId: this.siteId,
        installerUserId: this.installerUserId,
        ticketId: this.orderId,
        operateType: 2
      }

      await handleDeviceShareInfo(params)

      updateMessageStatus({
        msgType: 3, // 消息类型， 1告警2呼叫3通知
        updateMsgList: [
          {
            id: this.msgId,
            status: 3 // 1、已读 2、同意 3、拒绝
          }
        ]
      })

      this.currentSite.status = applicationStatus.rejected
      this.cancelShareVisible = false
      this.$toastSuccess(this.$t('rejectMsg'))
      this.refreshRequest()
    },
    async approveApplication() {
      const params = {
        siteId: this.siteId,
        installerUserId: this.installerUserId,
        ticketId: this.orderId,
        operateType: 1
      }

      try {
        await handleDeviceShareInfo(params)

        updateMessageStatus({
          msgType: 3, // 消息类型， 1告警2呼叫3通知
          updateMsgList: [
            {
              id: this.msgId,
              status: 2 // 1、已读 2、同意 3、拒绝
            }
          ]
        })

        this.currentSite.status = applicationStatus.approved
        this.$toastSuccess(this.$t('agreed'))
        this.refreshRequest()
      } catch (error) {
        console.error(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-wrapper {
  background-color: var(--bg-color-white, #ffffff);
  height: 100%;
  position: relative;
  overflow: auto;

  .tvt-better-scroll {
    height: calc(100% - 44px);
    box-sizing: border-box;
  }

  .detail-content {
    width: 100%;
    height: calc(100% - 60px);
    box-sizing: border-box;

    ::v-deep .better-scroll-box {
      background-color: var(--bg-color-white, #ffffff);
    }
    .padding-bottom {
      height: 80px;
    }
    .card {
      width: calc(100% - 40px);
      margin: 16px auto 0;
    }
    .site-hosting {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }
  .site-list {
    width: calc(100% - 40px);
    margin: 0 auto 0;
    padding-bottom: 100px;
    flex: 1;
    .site {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    .site-header {
      width: 100%;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      height: 48px;
      padding-top: 12px;
      padding-bottom: 12px;
    }
    .site-header-left {
      display: flex;
      align-items: center;
      color: var(--text-color-primary, #1a1a1a);
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      .site-icon {
        width: 24px;
        height: 24px;
        margin-right: 6px;
      }
      .site-desc-icon {
        width: 15px;
        margin-left: 5px;
      }
      .site-label {
        white-space: nowrap;
      }
    }
    .site-tool {
      flex: 1;
      margin-left: 6px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .site-name {
        color: var(--text-color-placeholder, #a3a3a3);
        text-align: right;
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: 'PingFang SC';
        font-size: var(--font-size-body1-size, 16px);
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        margin-right: 6px;
        width: 100%;
      }
    }
    .device {
      padding-left: 6px;
    }
    .device-header {
      box-sizing: border-box;
      width: 100%;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: left;
      box-shadow: 0px 1px 0px var(--bg-color-secondary, #eeeeee);

      .checked-icon {
        width: 24px;
        margin-right: 6px;
      }

      .device-name {
        color: var(--text-color-primary, #1a1a1a);
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: 'PingFang SC';
        font-size: var(--font-size-body1-size, 16px);
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        width: 100%;
      }
      .device-status {
        color: var(--text-color-placeholder, #a3a3a3);
        font-family: 'PingFang SC';
        font-size: var(--font-size-text-size, 12px);
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
    }
    .device-content {
      padding-left: 30px;
      .device-permission {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        height: 40px;
        box-shadow: 0px 1px 0px var(--bg-color-secondary, #eeeeee);
      }
      .device-permission-title {
        align-self: stretch;
        color: var(--icon-color-primary, #2b2b2b);
        font-family: 'PingFang SC';
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
      .device-content-icon {
        width: 24px;
        height: 24px;
      }
    }

    .device-title {
      align-self: stretch;
      color: var(--text-color-primary, #82879b);
      font-family: 'PingFang SC';
      font-size: var(--font-size-text-size, 12px);
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      margin: 10px 0;
    }
    .device-item {
      display: flex;
      padding: 12px 0;
      align-items: center;
      .device-info {
        max-width: calc(100% - 85px);
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .device-icon-wrapper {
        width: 70px;
        height: 44px;

        border-radius: 2px;
        margin-right: 7px;
        background-image: url('@/assets/img/common/device.png');
        background-position: center;
        background-clip: border-box;
        background-size: cover;
      }
      .device-name {
        color: var(--text-color-primary, #1a1a1a);
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: 'PingFang SC';
        font-size: var(--font-size-body1-size, 16px);
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        margin-bottom: 3px;
      }
      .device-permission,
      .device-time {
        color: var(--text-color-placeholder, #9a9ca2);
        font-family: 'PingFang SC';
        font-size: var(--font-size-text-size, 12px);
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        margin-bottom: 3px;
      }
    }
    .empty-list {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      box-sizing: border-box;
      margin-top: 10%;

      .empty-img {
        width: 120px;
        margin-bottom: 25px;
      }
      .empty-info {
        color: var(--text-color-placeholder, #a3a3a3);
        text-align: center;
        font-family: 'PingFang SC';
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
    }
  }
  .host-remark {
    width: 100%;
    box-sizing: border-box;
    margin: 20px auto 0;
    background: var(--brand-bg-color-light-disabled, #f2f4f8);
    border-radius: 8px;
  }
  .footer-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    padding-bottom: 20px;
    justify-content: center;
    background-color: var(--bg-color-white, #ffffff);
  }
  .footer-btn {
    width: 167px;
    height: 40px;
    border: 1px solid var(--bg-color-secondary, #eeeeee);
    border-radius: 23px;
    line-height: 40px;
    text-align: center;
    margin-right: 10px;

    &:last-child {
      margin-right: 0;
    }
    &.primary {
      background: var(--brand-bg-color-active, #1d71f3);
    }
    &.disabled {
      background: #d2e1fe;
      width: 327px;
    }
  }
  .gray-gap {
    height: 8px;
    background: var(--brand-bg-color-light-disabled, #f2f4f8);
    margin-top: 16px;
  }
  .hosting-tip {
    box-sizing: border-box;
    width: calc(100% - 40px);
    padding-top: 8px;
    padding-right: 16px;
    padding-bottom: 8px;
    padding-left: 16px;
    margin: 8px auto 0;
    border-radius: 6px;
    background: var(--brand-bg-color-light-disabled, #f2f4f8);
    color: var(--text-color-secondary, #424344);
    font-family: PingFang SC;
    font-weight: 400;
    font-size: var(--font-size-text-size, 12px);
    line-height: 20px;
  }
}
</style>
<style lang="scss">
.service-detail-site-popup-container {
  background: var(--brand-bg-color-light-disabled, #f2f4f8);

  .site-change-item {
    height: 52px;
    border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
    background: #ffffff00;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color-primary, #1a1a1a);
    text-align: center;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: 'PingFang SC';
    font-size: var(--font-size-body1-size, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    background-color: var(--bg-color-white, #ffffff);
  }
  .cancel-btn {
    margin-top: 8px;
  }
}
.service-detail-change-name-popup-container {
  height: 196px;
  .header {
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 52px;
    padding: 16px;
    background-color: var(--bg-color-white, #ffffff);
    .cancel {
      color: var(--text-color-secondary, #50546d);
      text-align: right;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
    }
    .title {
      color: var(--text-color-primary, #101d34);
      text-align: center;
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
    .confirm {
      color: var(--brand-bg-color-active, #1d71f3);
      text-align: right;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;

      &.disabled {
        color: var(--text-color-placeholder, #a3a3a3);
      }
    }
  }
  .content {
    width: 100%;
    height: 124px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .content-input {
    width: 300px;
    padding: 14px 0;
    color: var(--text-color-primary, #101d34);
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: 'PingFang SC';
    font-size: var(--font-size-body1-size, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
  }
}
</style>
